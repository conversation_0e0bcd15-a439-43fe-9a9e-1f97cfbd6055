@article{2022Inspection,
  title={Inspection and identification of transmission line insulator breakdown based on deep learning using aerial images},
  author={ <PERSON>, <PERSON>  and  <PERSON>, J. C.  and  Sanyal, Alok },
  journal={Electric Power Systems Research},
  year={2022},
}

@article{2024Region,
  title={Region-Based Active Learning for Insulator Defect Diagnosis Using Aerial Images of Electric Transmission Networks},
  author={ <PERSON><PERSON>, <PERSON><PERSON>  and  <PERSON>, <PERSON>  and  <PERSON>, <PERSON>  and  <PERSON>, <PERSON>  and  <PERSON>, <PERSON> },
  journal={Power Delivery, IEEE Trans. on (T-PWRD)},
  volume={39},
  number={5},
  pages={13},
  year={2024},
}

@article{2023Summary,
  title={Summary of insulator defect detection based on deep learning},
  author={ <PERSON>, <PERSON>  and  <PERSON>, <PERSON>  and  Dong, J<PERSON>  and  <PERSON>, <PERSON> },
  journal={Electric Power Systems Research},
  year={2023},
}

@article{2022An,
  title={An Improved CenterNet Model for Insulator Defect Detection Using Aerial Imagery},
  author={ <PERSON><PERSON>, Haiyang  and  <PERSON>, Baohua  and  <PERSON>, <PERSON>  and  <PERSON>, <PERSON> },
  journal={Sensors (14248220)},
  volume={22},
  number={8},
  year={2022},
}

@article{2021Real,
  title={Real-Time Detection and Spatial Localization of Insulators for UAV Inspection Based on Binocular Stereo Vision},
  author={ Ma, Yunpeng  and  Li, Qingwu  and  <PERSON>, Lulu  and  <PERSON>, Yaqin  and  Xu, Chang },
  journal={Remote Sensing},
  volume={13},
  number={2},
  pages={230},
  year={2021},
}

@ARTICLE{9400959,
  author={El-Hag, Ayman},
  journal={IEEE Instrumentation \& Measurement Magazine}, 
  title={Application of Machine Learning in Outdoor Insulators Condition Monitoring and Diagnostics}, 
  year={2021},
  volume={24},
  number={2},
  pages={101-108},
  keywords={Condition monitoring;Machine learning;Insulators;Power grids;Generators;Monitoring;Power transformer insulation;Power generation},
  doi={10.1109/MIM.2021.9400959}}

@article{2023Accurate,
  title={Accurate Glass Insulators Defect Detection in Power Transmission Grids Using Aerial Image Augmentation},
  author={ Cao, Yuan  and  Xu, Hao  and  Su, Chao  and  Yang, Qiang },
  journal={Power Delivery, IEEE Trans. on (T-PWRD)},
  volume={38},
  number={2},
  pages={10},
  year={2023},
}

@article{2021InsuDet,
  title={InsuDet: A Fault Detection Method for Insulators of Overhead Transmission Lines Using Convolutional Neural Networks},
  author={ Zhang, Xingtuo  and  Zhang, Yiyi  and  Liu, Jiefeng  and  Zhang, Chaohai  and  Xue, Xueyue  and  Zhang, Heng  and  Zhang, Wei },
  journal={IEEE Transactions on Instrumentation and Measurement},
  number={70-},
  year={2021},
}

@article{2022Improved,
  title={Improved Algorithm for Insulator and Its Defect Detection Based on YOLOX},
  author={ Qin, Liang },
  journal={Sensors},
  volume={22},
  year={2022},
}

@article{2021Improved,
  title={Improved YOLOv3 Network for Insulator Detection in Aerial Images with Diverse Background Interference},
  author={ Liu, Chuanyang  and  Wu, Yiquan  and  Liu, Jingjing  and  Sun, Zuo },
  journal={Electronics},
  volume={10},
  number={7},
  pages={771},
  year={2021},
}

@Article{coatings13050880,
AUTHOR = {Chang, Rong and Zhou, Shuai and Zhang, Yi and Zhang, Nanchuan and Zhou, Chengjiang and Li, Mengzhen},
TITLE = {Research on Insulator Defect Detection Based on Improved YOLOv7 and Multi-UAV Cooperative System},
JOURNAL = {Coatings},
VOLUME = {13},
YEAR = {2023},
NUMBER = {5},
ARTICLE-NUMBER = {880},
URL = {https://www.mdpi.com/2079-6412/13/5/880},
ISSN = {2079-6412},
ABSTRACT = {Insulator self-blasts, cracked insulators, and bird nests often lead to large-scale power outages and safety accidents, while the detection system based on a single UAV and YOLOv7 is difficult to meet the speed and accuracy requirements in actual detection. Therefore, a novel insulator defect detection method based on improved YOLOv7 and a multi-UAV collaborative system is proposed innovatively. Firstly, a complete insulator defects dataset is constructed, and the introduction of insulator self-blasts, cracked insulators, and bird nest images avoids the problem of low reliability for single defect detection. Secondly, a multi-UAV collaborative platform is proposed, which improves the search scope and efficiency. Most critically, an improved YOLOv7-C3C2-GAM is proposed. The introduction of the C3C2 module and the CNeB2 structure improves the efficiency and accuracy of feature extraction, and the introduction of a global attention mechanism (GAM) improved the feature extraction ability to extract key information about small targets or occluded targets and feature in the region of interest. Compared with YOLOv7, the accuracies of YOLOv7-C3C2 and YOLOv7-C3C2-GAM are improved by 1.3% and 0.5%, respectively, the speed of YOLOv7-C3C2 is improved by 0.1 ms, and the lightweight sizes are reduced by 8.2 Mb and 8.1 Mb, respectively. Therefore, the proposed method provides theoretical and technical support for power equipment defect detection.},
DOI = {10.3390/coatings13050880}
}

@article{han2022insulator,
  title={Insulator breakage detection based on improved YOLOv5},
  author={Han, Gujing and He, Min and Gao, Mengze and Yu, Jinyun and Liu, Kaipei and Qin, Liang},
  journal={Sustainability},
  volume={14},
  number={10},
  pages={6066},
  year={2022},
  publisher={MDPI}
}

@article{DBLP:journals/eaai/FengYYYLSZ25,
  author       = {Fu Feng and
                  Xiaoxia Yang and
                  Ronghao Yang and
                  Hao Yu and
                  Fangzhou Liao and
                  Qiqi Shi and
                  Feng Zhu},
  title        = {An Insulator defect detection network combining bidirectional feature
                  pyramid network and attention mechanism in unmanned aerial vehicle
                  images},
  journal      = {Eng. Appl. Artif. Intell.},
  volume       = {152},
  pages        = {110745},
  year         = {2025},
  url          = {https://doi.org/10.1016/j.engappai.2025.110745},
  doi          = {10.1016/J.ENGAPPAI.2025.110745},
  timestamp    = {Fri, 23 May 2025 21:09:09 +0200},
  biburl       = {https://dblp.org/rec/journals/eaai/FengYYYLSZ25.bib},
  bibsource    = {dblp computer science bibliography, https://dblp.org}
}

@article{2024CACS-YOLO,
  title={CACS-YOLO: A Lightweight Model for Insulator Defect Detection Based on Improved YOLOv8m},
  author={ Cao, Zhong  and  Chen, Kaihong  and  Chen, Junzuo  and  Chen, Zhaohui  and  Zhang, Man },
  journal={IEEE Transactions on Instrumentation and Measurement},
  pages={73},
  year={2024},
}

@software{yolo11,
  author = {Glenn Jocher and Jing Qiu},
  title = {{Ultralytics YOLO11}},
  version = {11.0.0},
  year = {2024},
  url = {https://github.com/ultralytics/ultralytics},
  orcid = {0000-0001-5950-6979, 0000-0002-7603-6750, 0000-0003-3783-7069},
  license = {AGPL-3.0}
}

@inproceedings{2021An,
  title={An Image is Worth 16x16 Words: Transformers for Image Recognition at Scale},
  author={ Dosovitskiy, Alexey  and  Beyer, Lucas  and  Kolesnikov, Alexander  and  Weissenborn, Dirk  and  Zhai, Xiaohua  and  Unterthiner, Thomas  and  Dehghani, Mostafa  and  Minderer, Matthias  and  Heigold, Georg  and  Gelly, Sylvain },
  booktitle={International Conference on Learning Representations},
  year={2021},
}

@inproceedings{zhang2022epsanet,
  title={EPSANet: An efficient pyramid squeeze attention block on convolutional neural network},
  author={Zhang, Hu and Zu, Keke and Lu, Jian and Zou, Yuru and Meng, Deyu},
  booktitle={Proceedings of the asian conference on computer vision},
  pages={1161--1177},
  year={2022}
}

@inproceedings{shi2024transnext,
  title={Transnext: Robust foveal visual perception for vision transformers},
  author={Shi, Dai},
  booktitle={Proceedings of the IEEE/CVF conference on computer vision and pattern recognition},
  pages={17773--17783},
  year={2024}
}

@inproceedings{li2024rethinking,
  title={Rethinking Features-Fused-Pyramid-Neck for Object Detection},
  author={Li, Hulin},
  booktitle={European Conference on Computer Vision},
  pages={74--90},
  year={2024},
  organization={Springer}
}

@inproceedings{chollet2017xception,
  title={Xception: Deep learning with depthwise separable convolutions},
  author={Chollet, Fran{\c{c}}ois},
  booktitle={Proceedings of the IEEE conference on computer vision and pattern recognition},
  pages={1251--1258},
  year={2017}
}

@article{hendrycks2016gaussian,
  title={Gaussian error linear units (gelus)},
  author={Hendrycks, Dan and Gimpel, Kevin},
  journal={arXiv preprint arXiv:1606.08415},
  year={2016}
}

@inproceedings{zhang2018shufflenet,
  title={Shufflenet: An extremely efficient convolutional neural network for mobile devices},
  author={Zhang, Xiangyu and Zhou, Xinyu and Lin, Mengxiao and Sun, Jian},
  booktitle={Proceedings of the IEEE conference on computer vision and pattern recognition},
  pages={6848--6856},
  year={2018}
}

@inproceedings{tian2019fcos,
  title={Fcos: Fully convolutional one-stage object detection},
  author={Tian, Zhi and Shen, Chunhua and Chen, Hao and He, Tong},
  booktitle={Proceedings of the IEEE/CVF international conference on computer vision},
  pages={9627--9636},
  year={2019}
}

@inproceedings{wu2018group,
  title={Group normalization},
  author={Wu, Yuxin and He, Kaiming},
  booktitle={Proceedings of the European conference on computer vision (ECCV)},
  pages={3--19},
  year={2018}
}

@inproceedings{li2021generalized,
  title={Generalized focal loss v2: Learning reliable localization quality estimation for dense object detection},
  author={Li, Xiang and Wang, Wenhai and Hu, Xiaolin and Li, Jun and Tang, Jinhui and Yang, Jian},
  booktitle={Proceedings of the IEEE/CVF conference on computer vision and pattern recognition},
  pages={11632--11641},
  year={2021}
}

@article{tao2018detection,
  title={Detection of Power Line Insulator Defects Using Aerial Images Analyzed With Convolutional Neural Networks},
  author={Tao, Xian and Zhang, Dapeng and Wang, Zihao and Liu, Xilong and Zhang, Hongyan and Xu, De},
  journal={IEEE Transactions on Systems, Man, and Cybernetics: Systems},
  year={2018},
  publisher={IEEE}
}

@data{vkdw-x769-21,
doi = {10.21227/vkdw-x769},
url = {https://dx.doi.org/10.21227/vkdw-x769},
author = {Dexter Lewis and Pratik Kulkarni},
publisher = {IEEE Dataport},
title = {Insulator Defect Detection},
year = {2021} }

@inproceedings{selvaraju2017grad,
  title={Grad-cam: Visual explanations from deep networks via gradient-based localization},
  author={Selvaraju, Ramprasaath R and Cogswell, Michael and Das, Abhishek and Vedantam, Ramakrishna and Parikh, Devi and Batra, Dhruv},
  booktitle={Proceedings of the IEEE international conference on computer vision},
  pages={618--626},
  year={2017}
}

@article{liu2024yolo,
  title={LA-YOLO: Bidirectional adaptive feature fusion approach for small object detection of insulator self-explosion defects},
  author={Liu, Bao and Jiang, Wenqiang},
  journal={IEEE Transactions on Power Delivery},
  year={2024},
  publisher={IEEE}
}

@article{jin2025real,
  title={A Real-Time Edge Inference Method for Insulator Contamination Detection with YOLOv11-ssL},
  author={Jin, Lijun and Ding, Wendi and Han, Shijia and Wang, Jinyu},
  journal={IEEE Transactions on Instrumentation and Measurement},
  year={2025},
  publisher={IEEE}
}

@ARTICLE{10471592,
  author={Yang, Guoyu and Lei, Jie and Tian, Hao and Feng, Zunlei and Liang, Ronghua},
  journal={IEEE Transactions on Circuits and Systems for Video Technology}, 
  title={Asymptotic Feature Pyramid Network for Labeling Pixels and Regions}, 
  year={2024},
  volume={34},
  number={9},
  pages={7820-7829},
  keywords={Feature extraction;Semantics;Computer architecture;Task analysis;Fuses;Semantic segmentation;Object detection;Object detection;semantic segmentation;feature pyramid network;asymptotic fusion;adaptive spatial fusion},
  doi={10.1109/TCSVT.2024.3376773}}

@inproceedings{tan2020efficientdet,
  title={Efficientdet: Scalable and efficient object detection},
  author={Tan, Mingxing and Pang, Ruoming and Le, Quoc V},
  booktitle={Proceedings of the IEEE/CVF conference on computer vision and pattern recognition},
  pages={10781--10790},
  year={2020}
}

@article{wu2025crl,
  title={CRL-YOLO: A Comprehensive Recalibration and Lightweight Detection Model for UAV Power Line Inspections},
  author={Wu, Dong and Yang, Weijiang and Li, Jiechang and Du, Kaiyue and Li, Linrong and Yang, Zhen},
  journal={IEEE Transactions on Instrumentation and Measurement},
  year={2025},
  publisher={IEEE}
}

@article{ren2016faster,
  title={Faster R-CNN: Towards real-time object detection with region proposal networks},
  author={Ren, Shaoqing and He, Kaiming and Girshick, Ross and Sun, Jian},
  journal={IEEE transactions on pattern analysis and machine intelligence},
  volume={39},
  number={6},
  pages={1137--1149},
  year={2016},
  publisher={IEEE}
}

@inproceedings{cai2018cascade,
  title={Cascade r-cnn: Delving into high quality object detection},
  author={Cai, Zhaowei and Vasconcelos, Nuno},
  booktitle={Proceedings of the IEEE conference on computer vision and pattern recognition},
  pages={6154--6162},
  year={2018}
}

@inproceedings{zhang2020dynamic,
  title={Dynamic R-CNN: Towards high quality object detection via dynamic training},
  author={Zhang, Hongkai and Chang, Hong and Ma, Bingpeng and Wang, Naiyan and Chen, Xilin},
  booktitle={European conference on computer vision},
  pages={260--275},
  year={2020},
  organization={Springer}
}

@inproceedings{pang2019libra,
  title={Libra r-cnn: Towards balanced learning for object detection},
  author={Pang, Jiangmiao and Chen, Kai and Shi, Jianping and Feng, Huajun and Ouyang, Wanli and Lin, Dahua},
  booktitle={Proceedings of the IEEE/CVF conference on computer vision and pattern recognition},
  pages={821--830},
  year={2019}
}

@inproceedings{lin2017focal,
  title={Focal loss for dense object detection},
  author={Lin, Tsung-Yi and Goyal, Priya and Girshick, Ross and He, Kaiming and Doll{\'a}r, Piotr},
  booktitle={Proceedings of the IEEE international conference on computer vision},
  pages={2980--2988},
  year={2017}
}

@article{yolov3,
  title={{YOLOv3: An incremental improvement}},
  author={Redmon, Joseph and Farhadi, Ali},
  journal={arXiv preprint arXiv:1804.02767},
  year={2018}
}

@software{yolov5,
  title = {{Ultralytics YOLOv5}},
  author = {Glenn Jocher},
  year = {2020},
  version = {7.0},
  license = {AGPL-3.0},
  url = {https://github.com/ultralytics/yolov5},
  doi = {10.5281/zenodo.3908559},
  orcid = {0000-0001-5950-6979}
}

@article{yolov6,
  title={{YOLOv6 v3.0: A full-scale reloading}},
  author={Li, Chuyi and Li, Lulu and Geng, Yifei and Jiang, Hongliang and Cheng, Meng and Zhang, Bo and Ke, Zaidan and Xu, Xiaoming and Chu, Xiangxiang},
  journal={arXiv preprint arXiv:2301.05586},
  year={2023}
}

@article{yolov7,
  title={{YOLOv7: Trainable bag-of-freebies sets new state-of-the-art for real-time object detectors}},
  author={Wang, Chien-Yao and Bochkovskiy, Alexey and Liao, Hong-Yuan Mark},
  journal={arXiv preprint arXiv:2207.02696},
  year={2022}
}

@software{yolov8,
  author = {Glenn Jocher and Ayush Chaurasia and Jing Qiu},
  title = {{Ultralytics YOLOv8}},
  version = {8.0.0},
  year = {2023},
  url = {https://github.com/ultralytics/ultralytics},
  orcid = {0000-0001-5950-6979, 0000-0002-7603-6750, 0000-0003-3783-7069},
  license = {AGPL-3.0}
}

@article{yolov9,
  title={{YOLOv9: Learning what you want to learn using programmable gradient information}},
  author={Wang, Chien-Yao  and Liao, Hong-Yuan Mark},
  journal={arXiv preprint arXiv:2402.13616},
  year={2024}
}

@article{yolov10,
  title={Yolov10: Real-time end-to-end object detection},
  author={Wang, Ao and Chen, Hui and Liu, Lihao and Chen, Kai and Lin, Zijia and Han, Jungong and others},
  journal={Advances in Neural Information Processing Systems},
  volume={37},
  pages={107984--108011},
  year={2024}
}

@article{yolov12,
  title={{YOLOv12: Attention-centric real-time object detectors}},
  author={Tian, Yunjie and Ye, Qixiang and Doermann, David},
  journal={arXiv preprint arXiv:2502.12524},
  year={2025}
}

@article{wang2024mci,
  title={MCI-GLA plug-in suitable for YOLO series models for transmission line insulator defect detection},
  author={Wang, Yaru and Song, Xiaoke and Feng, Lilong and Zhai, Yongjie and Zhao, Zhenbing and Zhang, Shiyin and Wang, Qianming},
  journal={IEEE Transactions on Instrumentation and Measurement},
  volume={73},
  pages={1--12},
  year={2024},
  publisher={IEEE}
}

@article{tao2024snakenet,
  title={SnakeNet: An adaptive network for small object and complex background for insulator surface defect detection},
  author={Tao, Zhiyong and He, Yan and Lin, Sen and Yi, Tingjun and Li, Minglang},
  journal={Computers and Electrical Engineering},
  volume={117},
  pages={109259},
  year={2024},
  publisher={Elsevier}
}

@article{pradeep2025improved,
  title={An improved transfer learning model for detection of insulator defects in power transmission lines},
  author={Pradeep, V and Baskaran, K and Evangeline, S Ida},
  journal={Neural Computing and Applications},
  volume={37},
  number={9},
  pages={6951--6976},
  year={2025},
  publisher={Springer}
} 