

# YOLO-CEA: Context-Enhanced and Aligned  Algorithm  for Insulator Defect Detection in Power  Transmission Lines

## Abstract

Accurate and efficient detection of insulator defects is crucial for ensuring the stability and safety of power transmission systems. However, this task is challenged by complex backgrounds, significant scale variations of defects, and the strict computational constraints of real-time inspection platforms like unmanned aerial vehicles (UAVs). To address these challenges, this paper introduces YOLO-CEA (Context-Enhanced and Aligned YOLO), a novel, lightweight, and high-performance detection framework systematically re-engineered from YOLOv11. YOLO-CEA integrates three core innovations: 1) A **C3k2-HTC** module in the backbone, which synergistically fuses Transformer-based global context with CNN-based local features to enhance feature representation in complex scenes. 2) An **Aligned-Balanced Feature Pyramid Network (AB-FPN)** in the neck, which employs Soft Nearest Neighbor Interpolation (SNI) and GSConv Enhancement (GSConvE) to achieve precise multi-scale feature alignment and an optimal balance between computational cost and information richness. 3) A **Shared Location Quality Modulation Head (SharedLQMHead)**, which improves parameter efficiency and introduces a novel mechanism to align classification confidence with localization accuracy, thereby enhancing the reliability of detection results. Experimental results on the public IDID dataset demonstrate that YOLO-CEA achieves an AP50-95 of 87.81%, a significant **4.78 percentage point improvement** over the baseline, while simultaneously reducing GFLOPs to **5.4** and model size to **4.6 MB**. Notably, YOLO-CEA surpasses recent heavyweight models in key metrics with only a fraction of their computational cost, and extensive tests on the CPLID dataset further validate its superior performance and strong generalization ability. The proposed model achieves a state-of-the-art balance between detection accuracy and computational efficiency, demonstrating significant potential for deployment in automated, resource-constrained aerial inspection systems.

**Keywords:** Insulator Defect Detection, YOLO, Lightweight Object Detection, Feature Fusion, Attention Mechanism, Transformer.

---

## INTRODUCTION

Insulators are fundamental components of high-voltage transmission lines, providing essential electrical insulation and mechanical support. However, their prolonged exposure to harsh environmental conditions and continuous electrical stress inevitably leads to defects such as breakage, contamination flashover, and self-shattering [1]. These defects critically compromise the insulation integrity of the power system, potentially triggering large-scale outages and incurring significant economic losses [2]. Consequently, the regular and accurate inspection of insulators has become a critical task for ensuring the safe and reliable operation of the power grid. While traditional manual inspections are fraught with high costs, low efficiency, and significant safety risks, the advent of Unmanned Aerial Vehicles (UAVs) equipped with high-resolution cameras has emerged as a transformative alternative, enabling safer, faster, and more comprehensive data acquisition [3, 4]. This technological shift has catalyzed the development of deep learning-based algorithms for the automated analysis of vast aerial imagery datasets.

Despite considerable progress, the automated detection of insulator defects from UAV imagery remains a formidable challenge, primarily due to three persistent issues. First, insulator targets are often situated against **highly complex and cluttered backgrounds**, including sky, terrain, vegetation, and intricate pylon structures, which can introduce significant noise and lead to false positives [5]. Second, the defects themselves exhibit **significant scale variation**, ranging from large, conspicuous breakage to minute, subtle cracks. This demands that a detection model possess robust multi-scale perception capabilities to identify defects across a wide spectrum of sizes [6]. Third, for practical field deployment on UAVs, the detection models must be **lightweight and computationally efficient** to adhere to the stringent constraints of on-board processing, such as limited memory, low power consumption, and the need for real-time inference [7]. This creates a fundamental tension between achieving high detection accuracy and maintaining low model complexity.

Many existing object detection models struggle to strike an optimal balance for this specific task. Two-stage detectors like Faster R-CNN [8], while often accurate, are typically too computationally intensive and slow for real-time applications on edge devices. Single-stage detectors from the YOLO (You Only Look Once) series have gained popularity due to their superior trade-off between speed and accuracy [9]. However, even state-of-the-art YOLO models exhibit inherent limitations when applied to insulator defect detection. Firstly, their standard backbone networks, built upon convolutional neural networks (CNNs), possess an intrinsically limited receptive field, hampering their ability to capture the **global contextual information** needed to distinguish small defects from complex background textures [10]. Secondly, their feature pyramid networks (FPNs) [11], used for multi-scale feature fusion, often rely on simple interpolation methods that can cause **feature misalignment**, degrading the quality of fused features and impairing the detection of objects at disparate scales. Finally, their detection heads typically treat classification and localization as decoupled tasks, often resulting in a **misalignment between high classification confidence and poor localization quality**, which reduces the overall reliability of the final predictions [12].

To overcome these multifaceted limitations, this paper proposes a novel lightweight and high-performance detector named **Context-Enhanced and Aligned YOLO (YOLO-CEA)**. Our approach systematically innovates upon the architecture's three core components—backbone, neck, and head—to specifically address the challenges of insulator defect detection. The main contributions of this work are summarized as follows:

1.  We propose a **Context-Enhanced Backbone** incorporating a novel **C3k2-HTC module**. This hybrid block synergistically integrates a Transformer with a CNN, enabling the model to capture both long-range dependencies and fine-grained local features. This significantly enhances feature representation, especially in cluttered background scenarios.

2.  We design an **Aligned-Balanced Feature Pyramid Network (AB-FPN)** for the neck. This novel FPN replaces conventional operators with **Soft Nearest Neighbor Interpolation (SNI)** to mitigate feature misalignment during upsampling and employs **GSConv Enhancement (GSConvE)** for an efficient yet information-rich downsampling path, leading to superior fusion of multi-scale features.

3.  We introduce a **Reliable and Efficient Head**, named **SharedLQMHead**. It utilizes cross-scale parameter sharing to reduce model complexity while integrating a novel **Location Quality Modulation (LQM)** mechanism. This mechanism explicitly aligns classification scores with localization accuracy, ensuring that the model yields highly reliable detections.

4.  We conduct extensive experiments on the public IDID and CPLID datasets. The results demonstrate that our proposed YOLO-CEA not only achieves state-of-the-art detection accuracy, significantly outperforming the baseline and other competing models, but also maintains exceptional computational efficiency, establishing a new benchmark for lightweight insulator defect detection.

## RELATED WORK

The field of insulator defect detection has evolved significantly, transitioning from manual inspection to sophisticated automated systems. This section reviews the trajectory of these methods, analyzing their respective strengths and limitations to contextualize the contributions of our proposed work.

Initial approaches to insulator inspection relied on manual patrols, a practice known to be labor-intensive, inefficient, and fraught with safety risks for personnel, particularly in challenging geographical and environmental conditions [10]. To mitigate these issues, early research explored automation through traditional computer vision techniques. These methods often utilized handcrafted features, such as directional angles and shape priors for localization [11] or local feature descriptors for classification [12]. However, their performance was inherently fragile, showing limited robustness to the vast variations in lighting, viewing angles, and background clutter encountered in real-world aerial imagery, thus hindering their practical deployment.

The paradigm shifted with the advent of deep learning, especially with the success of Convolutional Neural Networks (CNNs), which have demonstrated superior performance in object detection. Within this domain, methods are broadly categorized as two-stage or one-stage. Two-stage detectors, such as the R-CNN family [13-15], first generate a set of candidate region proposals and then classify each proposal. Several studies have successfully adapted these models for insulator detection, integrating components like Feature Pyramid Networks (FPN) [16] or Transformer-based attention mechanisms [18] to achieve high precision. Despite their accuracy, these models are characterized by high computational complexity and slow inference speeds, rendering them impractical for real-time applications on resource-constrained edge devices like UAVs [19].

Consequently, one-stage detectors, particularly the You Only Look Once (YOLO) series [20-28], have become the predominant choice for this task due to their excellent trade-off between speed and accuracy. Research in this area has largely focused on enhancing the standard YOLO architecture to address key challenges. To tackle the issue of significant **scale variation** in defects, some researchers have modified the network's detection head, for instance, by replacing it with a Transformer-based prediction head [14] or by adding an extra detection head for micro-scale targets [15]. While beneficial, these modifications often overlook the critical problem of **feature misalignment** caused by naive upsampling operations in the feature fusion process. To handle **complex backgrounds**, the integration of attention mechanisms like Coordinate Attention (CA) [16, 17] or CBAM [11, 19] has been a popular strategy. These modules help the network focus on salient object features, yet they predominantly capture **local correlations** and often fail to model the **long-range, global contextual information** necessary to distinguish small defects from similarly textured background noise. Furthermore, for practical **lightweight deployment**, many have explored replacing standard convolutions with more efficient alternatives like GhostConv [36, 39] or depth-wise separable convolutions [38]. While effective in reducing model parameters and FLOPs, this often comes at the cost of a noticeable drop in detection accuracy, creating a persistent trade-off.

Our review of the existing literature reveals a significant research gap. Current approaches tend to offer piecemeal solutions, addressing challenges like scale, background, or model complexity in isolation. There is a lack of a **holistic and systematic framework** that introduces coordinated innovations across all core components of the detector—backbone, neck, and head. This fragmented approach can lead to suboptimal performance, as enhancements in one part of the network may be bottlenecked by limitations in another. For example, rich features extracted by a powerful backbone can be degraded by a misaligned feature pyramid, or a well-localized bounding box can be wrongly suppressed due to an unreliable classification score. To bridge this gap, this paper proposes **YOLO-CEA**, a detector designed with synergistic enhancements. By introducing a context-aware backbone for robust feature extraction, an alignment-focused neck for superior multi-scale fusion, and a reliability-oriented head that synchronizes classification and localization, our work aims to establish a new state-of-the-art balance between accuracy, efficiency, and reliability for insulator defect detection.

## Method

This chapter elaborates on the novel framework we designed to enhance the performance of insulator defect detection. This framework systematically reconstructs and optimizes the backbone, feature fusion neck, and detection head of YOLOv11. We have named our new model **Context-Enhanced and Aligned YOLO (YOLO-CEA)**, and its overall architecture is shown in Figure 3.1. The core improvements of YOLO-CEA focus on three aspects: **feature context enhancement** through the introduction of a hybrid attention mechanism, **multi-scale feature alignment** by optimizing sampling strategies, and **improving the reliability of detection results** by refining the prediction head.

As shown in Figure 3.1, in the **Backbone** section, the input image first passes through a series of convolutional layers for downsampling to progressively extract multi-level feature maps. To fundamentally enhance the model's feature representation capabilities, we replaced the original C3k2 module in the deep layers of the backbone with our `C3k2-HTC` module. The `C3k2-HTC` module synergistically integrates the global context-capturing capabilities of Transformers with the fine-grained local feature extraction capabilities of convolutions, which is crucial for accurately identifying insulator defects of various morphologies in complex backgrounds. Subsequently, the `SPPF` module enhances the receptive field of features through multi-scale pooling operations, while the `C2PSA` module at the end of the backbone further strengthens the capture of key multi-scale information through its unique attention mechanism. This provides a richer and higher-quality feature foundation for the subsequent feature fusion network.

Next, these multi-scale features extracted from the backbone are fed into our designed **Aligned and Balanced Feature Pyramid Network (AB-FPN)** for feature fusion. AB-FPN ensures efficient interaction between features at different levels through two specially optimized paths (top-down and bottom-up). In the top-down path, we use the `SNI` module for upsampling. It employs an energy-aware soft interpolation method to effectively prevent high-level semantic features from excessively suppressing low-level detail features, thereby achieving precise feature alignment. In the bottom-up path, we use the `GSConvE` module for downsampling. This module significantly reduces computational costs while preserving rich feature information, achieving a perfect balance between computational efficiency and representational power. Through the synergistic action of `SNI` and `GSConvE`, AB-FPN generates multi-scale fused features that are better aligned and more information-balanced.

Finally, these fused features are passed to the **Shared Location Quality Modulation Detection Head (SharedLQMHead)** for final bounding box regression and class prediction. This detection head is designed to address the core issues of parameter redundancy and the mismatch between classification confidence and localization precision in traditional detectors. It first enhances parameter efficiency through a convolutional layer shared across scales. Then, it utilizes the innovative `LQM` module to dynamically adjust and calibrate classification scores based on the quality of the predicted bounding box probability distribution. This mechanism ensures that only prediction boxes with both high classification confidence and high localization accuracy receive a final high score, thereby significantly improving the reliability of the detection.

Through the coordinated operation of the above modules, YOLO-CEA demonstrates outstanding detection performance and efficiency in complex power line inspection scenarios. In the following subsections, we will provide an in-depth introduction to the design principles and implementation details of the `C3k2-HTC` module, the AB-FPN architecture, and the `SharedLQMHead`.

### **3.1 C3k2_HTC**

To effectively address the challenges of insulator defect detection in the complex backgrounds of power transmission lines, this paper optimizes the feature extraction capability of the YOLOv11 backbone. In the deep stages of the original YOLOv11 backbone, the model primarily relies on the `C3k2` module for feature extraction. As shown in Figure 3.1 (top left), the core computational unit of the `C3k2` module consists of multiple cascaded `Bottleneck` or C3k modules, which is a purely convolution-based design. Although this structure is efficient at capturing local textures and spatial features, its capabilities reveal significant shortcomings when dealing with complex scenes that require global contextual information. Due to the fixed size of convolutional kernels, computation is limited to a local receptive field, making it difficult to establish dependencies between distant pixels in an image. For example, when determining whether a tiny object is a genuine insulator self-explosion defect or complex background noise, the model needs to not only identify its local features but also understand its global position within the entire insulator string and even the transmission tower. The pure-convolution `C3k2` module is inadequate in this regard, easily leading to missed detections or false positives. To this end, this paper innovatively designs a **C3k2-HTC module (C3k2 module with Hybrid Transformer Conv Block)**, which aims to organically combine the powerful global modeling capabilities of Transformers with the efficient local feature extraction of CNNs, thereby fundamentally compensating for the network's deficiency in global information perception.

`C3k2-HTC` replaces the C3k in C3k2 with the **HTCBlock (Hybrid Transformer Conv Block)**. As shown in Figure 3.1, the `HTCBlock` is key to achieving parallel processing of local and global information. It first divides the channels of the input feature map according to an adjustable mixing ratio hyperparameter $r$. A proportion $r$ of the channels is sent to a **TransformerBlock** branch. This branch not only includes a standard Multi-Head Self-Attention (MHSA) module to capture long-range dependencies, but its Feed-Forward Network (FFN) part has also been critically optimized. Compared to the conventional design in Vision Transformers that uses simple linear layers and GELU activation functions, we introduce the **Gated Linear Unit (CGLU)**. CGLU uses a dynamic, data-driven gating mechanism to regulate information flow, replacing the fixed activation function. This endows the FFN with stronger expressive power and non-linear modeling potential, allowing it to process and transform features more adaptively, thereby further improving the quality of extracted global contextual information. Meanwhile, the remaining proportion $1-r$ of channels enters a standard **Bottleneck** convolution branch to preserve and refine local spatial details and texture features. The outputs of these two branches are concatenated along the channel dimension and deeply fused through a $1 \times 1$ convolutional layer. This parallel hybrid structure enables the model to perceive both local details and global context within a single module, achieving a complementary advantage between local and global information.

In the overall design of `C3k2_HTC`, the input features first pass through a $1 \times 1$ convolution and are then split into two paths. One path serves as a direct shortcut connection, while the other is sent into a sequence of multiple cascaded `HTCBlock`s for deep feature extraction. Finally, the features from these two paths are concatenated and fused, a structure that greatly facilitates gradient flow and cross-layer feature reuse.

Considering that deep network layers process more high-level, abstract semantic features, establishing global dependencies on these features is more meaningful than on shallow-level, low-level texture features. Furthermore, the computational complexity of the self-attention mechanism in Transformers is quadratic with respect to the feature map size. Applying it in the deep layers of the network, where the spatial resolution is already significantly reduced, allows for efficient global context modeling at a controllable computational cost. Therefore, we deploy this module in the deep stages of the backbone to replace the original `C3k2` module, striking a balance between model performance and efficiency.

### 3.2 Aligned-Balanced Feature Pyramid Network (AB-FPN)

YOLOv11 employs the classic Path Aggregation Network (PANet) as its neck structure, achieving effective multi-scale feature fusion through top-down and bottom-up pathways. However, when dealing with targets like insulator defects, which exhibit vast size variations and are rich in detailed information, we found that the feature fusion mechanism of the original YOLOv11 neck still has room for optimization. Specifically, its top-down path uses **standard nearest-neighbor interpolation**, and its bottom-up path uses **standard strided convolution**, which can lead to **feature misalignment** and **computational redundancy**, respectively.

To address these challenges, we have deeply optimized the neck of YOLOv11 and proposed a new feature fusion architecture—the **Aligned-Balanced Feature Pyramid Network (AB-FPN)**. The core idea of AB-FPN is to replace the original upsampling and downsampling operations with two specially designed lightweight modules, namely **Soft Nearest Neighbor Interpolation (SNI)** and **GSConv Enhancement (GSConvE)**. This achieves better feature alignment and computational balance with almost no additional cost.

In the original top-down path of YOLOv11, high-level feature maps are enlarged using standard nearest-neighbor interpolation. This "hard" pixel replication causes the energy (sum of activation values) of the feature map to grow quadratically with the magnification factor, leading to high-level semantic features dominating the fusion with low-level detail features. This imbalanced fusion can easily cause feature misalignment, which is particularly detrimental to preserving weak features such as tiny cracks on insulators. The SNI module aims to "soften" this process. After performing nearest-neighbor interpolation, it introduces a softening coefficient `α`, which is related to the scaling factor, to normalize the energy of the result. Its operation can be defined by Equation (3.1):

$$
\mathbf{Y}= \alpha \cdot f_{\text{nearest}}(\mathbf{X}) \quad \text{where} \quad \alpha = \frac{1}{k^2} \quad \cdots \quad (3.1)
$$
where $\mathbf{X}$ is the deep feature map to be upsampled, $\mathbf{Y}$ is the upsampled result, $f_{\text{nearest}}$ represents the nearest-neighbor interpolation operation, and $k$ is the upsampling factor.

By introducing `α`, SNI **regulates** the weights of features from different levels during fusion, effectively mitigating the drastic energy changes caused by upsampling and preventing high-level features from excessively "covering" low-level features. This makes the fusion process smoother, promotes **Secondary Features Alignment**, and significantly reduces the difficulty for subsequent convolutional layers to learn the fused representation, thereby enhancing the model's comprehensive perception of multi-scale defects.

In the original bottom-up path of YOLOv11, feature map downsampling is accomplished by a 3×3 convolution with a stride of 2. While effective, standard convolution has a relatively large number of parameters and computational load (FLOPs) when processing high-channel feature maps, presenting an optimization opportunity for detection models that pursue ultimate efficiency.

To reduce computational cost while maintaining or even enhancing feature representation capabilities, we employ the GSConvE module to perform the downsampling task. GSConvE is an efficient hybrid convolution structure, with its core idea being to strike a delicate balance between computational efficiency and feature richness. As shown in Figure 3.X, GSConvE cleverly decomposes the feature processing into two parallel branches. First, the input feature map flows through a standard convolutional layer, which reduces the number of input channels to half of the target number of channels. This step is responsible for extracting information-dense "base features" with cross-channel interactions. Subsequently, these generated "base features" are sent to an extremely lightweight secondary processing branch. This branch consists of a 2D convolution, a depth-wise separable convolution (DWC), and a GELU activation function in series. Since the computational cost of depth-wise separable convolution is extremely low, this branch can generate "enhanced features" with diverse receptive fields and texture information with almost no additional burden. Finally, the "base features" from the dense path and the "enhanced features" from the sparse path are concatenated along the channel dimension. To break the channel-wise separation of features from the two paths and promote full information interaction, a crucial **Channel Shuffle** operation is applied to the concatenated feature map. This operation efficiently recombines features from different computational paths, ensuring that information is thoroughly mixed before being passed to the next layer.

Essentially, GSConvE adopts a "divide and conquer" strategy: it lets the computationally expensive standard convolution focus on extracting the most critical cross-channel information, while assigning the task of generating feature diversity to the extremely low-cost depth-wise convolution. Compared to the single standard convolution in the original YOLOv11, this design achieves an excellent **balance** between **computational efficiency** (from sparse depth-wise convolution) and **feature richness** (from dense standard convolution). It can provide the model with stronger representation capabilities while significantly reducing the number of parameters and computational load, thereby improving the efficiency of feature aggregation in the bottom-up path.

In summary, our proposed AB-FPN architecture performs targeted optimizations on the key components of the original YOLOv11 neck through `SNI` and `GSConvE`. It systematically enhances the performance of the feature pyramid from the dimensions of "alignment" and "balance," laying a solid foundation for high-precision, high-efficiency insulator defect detection.

### **3.3 Shared Location Quality Modulation Detection Head (SharedLQMHead)**

Detection heads in modern object detectors generally face two interrelated challenges: first, **parameter efficiency**, which refers to the parameter redundancy and computational overhead caused by deploying independent prediction branches for each level of the multi-scale feature pyramid; and second, **prediction reliability**, which is the problem of a lack of effective information interaction between the parallel tasks of classification and localization, leading to inconsistency between classification confidence and localization precision. To systematically address these challenges, we propose a novel detection head architecture—the **Shared Location Quality Modulation Detection Head (SharedLQMHead)**. This architecture collaboratively optimizes parameter efficiency and prediction reliability through a unified design.

The core design of SharedLQMHead includes two interrelated mechanisms: **cross-scale parameter sharing** and **location quality modulation**.

First, to improve parameter efficiency and enhance the generalization capability of feature representations, we introduce a **cross-scale parameter sharing mechanism**. Unlike traditional designs that equip each FPN level with an independent convolutional layer, SharedLQMHead inputs feature maps from all scales into a **unified, parameter-shared convolutional module**. This design not only significantly reduces the number of parameters in the detection head but, more importantly, it forces the network to use the same set of transformation kernels across different scales, prompting the model to learn a scale-invariant feature extraction paradigm. The resulting shared feature representation has stronger generalization ability and provides high-quality input for the subsequent classification and regression tasks.

Second, based on these efficient shared features, we introduce the **Location Quality Modulation (LQM) mechanism** to address the inconsistency between classification and regression. This mechanism stems from a core insight: the probability distribution shape of bounding box regression can reflect its localization certainty. An accurate localization prediction should have a low-entropy discrete probability distribution $P_{box}$, meaning the distribution is sharp and concentrated. Conversely, an uncertain prediction corresponds to a high-entropy, flat distribution. LQM uses a lightweight multi-layer perceptron ($\mathcal{M}_{LQM}$) to map statistical measures extracted from $P_{box}$ (such as the mean of the top-k probability values) into a scalar score, which we call the **location quality score** ($S_{qual}$). Its calculation can be formalized as:
$$S_{qual} = \mathcal{M}_{LQM}(\text{Stat}(P_{box}))$$
This $S_{qual}$ score serves as an explicit quantification of localization quality and is used to modulate the initial classification score $S_{cls}$. The final confidence score $S_{final}$ is formed by their additive fusion:
$$S_{final} = S_{cls} + S_{qual}$$
In this way, localization quality is explicitly integrated into the final confidence assessment. For a prediction to achieve high confidence, it must satisfy both a high classification score and high localization quality. This mechanismically promotes synergy between the classification and regression tasks, enhancing the reliability of the final detection results.

In summary, the parameter sharing mechanism of SharedLQMHead first builds an efficient and generalizable feature extraction foundation, while the location quality modulation mechanism further calibrates the reliability of predictions on this basis. The organic combination of these two components forms a unified detection framework that is significantly improved in both efficiency and reliability.

##  EXPERIMENTAL

### Dataset

This study selected two widely recognized public datasets: the Chinese Power Line Insulator Dataset (CPLID) [22] and the Insulator Defect Image Dataset (IDID). The IDID dataset focuses on insulator strings on power transmission lines, where the main objects in the images are insulator bodies, classified into three categories based on their surface condition: **Good insulator shell**, **Broken insulator shell**, and **Flashover damage insulator shell**. It contains **1596 images** and **7568 annotated objects**, specifically including 1788 insulator strings, 2636 good shells, 1140 broken shells, and 2004 flashover shells. The CPLID dataset consists of 848 images, including 600 images of normal insulators and 248 images of Defective_Insulators. A notable feature of these defect images is that they are all artificially synthesized by manually pasting defective insulators onto different background images. Each dataset was split into training and validation sets at an 8:2 ratio.

### Experimental Setup

All experiments in this study were conducted on the same high-performance server, with consistent hardware and software environments to eliminate interference from computational platform differences on the experimental results. The core hardware for the experiments included an Intel Core i9-13900KF processor and an NVIDIA GeForce RTX 4090 graphics processor, the latter providing powerful parallel computing support for model training. On the software side, we implemented the algorithms based on Python 3.10 and the PyTorch 2.2.2 framework, and used the CUDA 12.1 library for GPU acceleration. The specific configuration information is shown in Table 1.

*   Table 1. Hardware and software configuration for the experiments.

| Item    | Value                                 |
| :------ | :------------------------------------ |
| OS      | Ubuntu 20.04 LTS                      |
| CPU     | 13th Gen Intel(R) Core(TM) i9-13900KF |
| GPU     | NVIDIA GeForce RTX 4090               |
| Python  | 3.10                                  |
| CUDA    | 12.1                                  |
| Pytorch | 2.2.2                                 |

In the implementation of model training, we unified key hyperparameter settings to ensure fair comparison. We used the Stochastic Gradient Descent (SGD) optimizer for end-to-end model training. The initial learning rate was set to 0.01, the momentum factor was 0.937, and a weight decay coefficient of 0.0005 was used to suppress overfitting. The entire training process consisted of 300 epochs. To improve data processing efficiency, we set the batch size to 16 and enabled 4 workers for parallel data loading. The detailed settings of all key hyperparameters are summarized in Table 2.

*   Table 2. Experimental parameters.

| Item                | Value  |
| :------------------ | :----- |
| Optimizer           | SGD    |
| Lr                  | 0.01   |
| Epochs              | 300    |
| Momentum            | 0.937  |
| Batch-size          | 16     |
| workers             | 4      |
| weight decay factor | 0.0005 |

### Evaluation Metrics

The model's performance is evaluated based on two key aspects: computational efficiency and detection accuracy. Model efficiency and complexity are assessed using three metrics. **Parameters** (M) represent the total number of learnable weights and biases, indicating the model's scale and memory footprint. **Model Size** (MB) is the disk space required to store the trained weights, a direct measure of storage requirements. **GFLOPs** (Giga Floating-point Operations) quantifies the computational workload for a single forward pass; a lower value signifies higher efficiency, which is critical for real-time deployment on resource-constrained devices. Detection accuracy is assessed through a hierarchy of metrics built upon the concepts of True Positives (TP), False Positives (FP), and False Negatives (FN). From these, we define **Precision**, the ratio of correct detections to all positive predictions, and **Recall**, the ratio of correct detections to all ground-truth objects.

$$
\text{Precision} = \frac{\text{TP}}{\text{TP} + \text{FP}}
$$

$$
\quad \text{Recall} = \frac{\text{TP}}{\text{TP} + \text{FN}}
$$

To balance the inherent trade-off between these two, the **F1-score** is calculated as their harmonic mean:
$$
\text{F1} = 2 \times \frac{\text{Precision} \times \text{Recall}}{\text{Precision} + \text{Recall}}
$$
For a more comprehensive single-class evaluation, **Average Precision (AP)** is used. Calculated as the area under the Precision-Recall (P-R) curve, AP summarizes performance across all confidence thresholds.
$$
\text{AP} = \int_{0}^{1} p(r) dr
$$
Finally, the **mean Average Precision (mAP)** serves as the primary overall metric, assessing performance across all *N* classes by averaging their individual AP scores.
$$
\text{mAP} = \frac{1}{N} \sum_{i=1}^{N} \text{AP}_i
$$

### Experimental Results Analysis

To comprehensively evaluate the performance of the proposed Context-Enhanced and Aligned YOLO (YOLO-CEA) model for insulator defect detection, we conducted a series of experiments on the public Insulator Defect Image Dataset (IDID). We first performed independent component validation for the core modules—C3k2_HTC, ABFPN, and SLQMHead—then examined the synergistic effects of each module through ablation studies, and finally compared the overall performance of our model with current mainstream object detection algorithms.

#### Experimental Validation and Analysis of the C3k2 Module

In the backbone network, our designed **C3k2-HTC module** integrates the global context-aware capabilities of Transformers with the local detail extraction capabilities of CNNs, aiming to enhance the model's ability to represent defect features in complex backgrounds. As shown in Table 1, compared to the baseline C3k2, C3k2-HTC improved the AP50 scores for breakage and flashover defects by 1.42% and 0.70%, reaching 97.78% and 98.15%, respectively. This result confirms that by processing local and global information in parallel and enhancing the feed-forward network with a Gated Linear Unit (CGLU), C3k2-HTC can extract more discriminative features. Notably, this performance gain was accompanied by a reduction in parameters and computational load (from 2.58M/6.3 GFLOPs to 2.45M/6.2 GFLOPs), fully demonstrating the efficiency and effectiveness of its design.

To further reveal its internal mechanism, we conducted a visualization analysis of the feature maps. As shown in Figure X, in cluttered background scenes, the heatmap activation areas of the baseline model are relatively scattered and easily disturbed by background noise. After introducing C3k2-HTC, the model can more accurately focus on the complete contour of the defect, effectively suppressing invalid activations in the background area. This intuitively demonstrates the module's role in enhancing target saliency and improving feature discriminability.

| Method      | Breakage AP50/% | Breakage AP50-95/% | Flashover AP50/% | Flashover AP50-95/% | Parameters(M) | GFLOPS | Weight Size(MB) |
| ----------- | --------------- | ------------------ | ---------------- | ------------------- | ------------- | ------ | --------------- |
| C3k2        | 96.36           | 77.00              | 97.45            | 86.63               | 2.58          | 6.3    | 5.2             |
| C3k2-faster | 91.01           | 70.30              | 95.72            | 82.30               | 2.28          | 5.8    | 4.7             |
| C3k2-star   | 95.08           | 77.06              | 97.44            | 86.77               | 2.47          | 6.4    | 5.1             |
| C3k2_HTC    | 97.78           | 80.41              | 98.15            | 86.84               | 2.45          | 6.2    | 5.0             |

#### Experimental Validation and Analysis of FPN

In the feature fusion neck, our designed **Aligned and Balanced Feature Pyramid Network (ABFPN)** demonstrated a significant advantage in multi-scale feature processing. According to the data in Table 2, ABFPN increased the AP50-95 for breakage defects from 77.00% to 80.85% with almost no increase in computational cost (6.3 GFLOPs), outperforming other FPN variants in overall performance. This is mainly attributed to its internal mechanisms: first, the SNI module in the top-down path effectively mitigates feature misalignment during upsampling through energy-aware soft interpolation; second, the GSConvE module in the bottom-up path reduces computational overhead while preserving rich feature information. Ultimately, ABFPN generates better-aligned and more information-balanced multi-scale feature maps, laying a solid foundation for subsequent accurate detection.

| Method  | Breakage AP50/% | Breakage AP50-95/% | Flashover AP50/% | Flashover AP50-95/% | Parameters(M) | GFLOPS | Weight Size(MB) |
| ------- | --------------- | ------------------ | ---------------- | ------------------- | ------------- | ------ | --------------- |
| FPN-PAN | 96.36           | 77.00              | 97.45            | 86.63               | 2.58          | 6.3    | 5.2             |
| AFPN    | 96.63           | 78.96              | 98.42            | 88.46               | 2.65          | 8.8    | 5.5             |
| BiFPN   | 96.13           | 78.53              | 98.09            | 86.30               | 1.92          | 6.3    | 4.0             |
| ABFPN   | 97.85           | 80.85              | 97.87            | 87.06               | 2.53          | 6.3    | 5.2             |

#### Experimental Validation and Analysis of the Head

At the detection head level, our proposed **Shared Location Quality Modulation Detection Head (SLQMHead)** aims to improve prediction reliability. As shown in Table 3, this detection head achieved the highest AP50 of 98.36% for flashover defect detection while significantly reducing the model's computational complexity (5.6 GFLOPs) and parameter count. This validates the effectiveness of its core LQM module, which dynamically calibrates classification confidence by evaluating the quality of localization predictions. This mechanism fundamentally promotes synergy between classification and regression tasks, ensuring the high reliability of detection results in terms of both class and location.

| Method   | Breakage AP50/% | Breakage AP50-95/% | Flashover AP50/% | Flashover AP50-95/% | Parameters(M) | GFLOPS | Weight Size(MB) |
| -------- | --------------- | ------------------ | ---------------- | ------------------- | ------------- | ------ | --------------- |
| Head     | 96.36           | 77.00              | 97.45            | 86.63               | 2.58          | 6.3    | 5.2             |
| LSCD     | 97.80           | 77.64              | 97.59            | 87.18               | 2.42          | 5.6    | 4.9             |
| LSCSBD   | 97.40           | 80.66              | 97.80            | 87.99               | 2.45          | 6.2    | 5.0             |
| SLQMHead | 96.96           | 77.96              | 98.36            | 87.36               | 2.42          | 5.6    | 4.9             |

#### Ablation Study

To investigate the synergistic gains among the innovative modules, we designed a comprehensive ablation study (see Table 4). The results clearly show a progressive improvement in performance. The baseline model's AP50-95 was 83.03%, and introducing any single module brought performance gains. Among them, ABFPN's contribution was the most significant, once again highlighting the central role of feature alignment in the insulator detection task.

| C3k2_HTC | ABFPN | SLQMH | GFLOPS  | Model Size(MB) |    P/%    |    R/%    |   F1/%    |  AP50/%   | AP50-95/% |
| :------: | :---: | :---: | :-----: | :------------: | :-------: | :-------: | :-------: | :-------: | :-------: |
|    ×     |   ×   |   ×   |   6.3   |      5.2       |   97.39   |   98.68   |   98.03   |   97.13   |   83.03   |
|    √     |   ×   |   ×   |   6.2   |      5.0       |   95.92   |   94.44   |   95.15   |   98.10   |   84.61   |
|    ×     |   √   |   ×   |   6.3   |      5.2       |   95.77   |   94.08   |   94.88   |   97.98   |   85.07   |
|    ×     |   ×   |   √   |   5.6   |      4.9       |   93.71   |   95.58   |   94.59   |   97.97   |   84.03   |
|    √     |   √   |   ×   |   6.1   |      4.9       |   95.72   |   92.78   |   94.16   |   97.45   |   84.17   |
|    √     |   ×   |   √   |   5.5   |      4.7       |   95.50   |   93.69   |   94.50   |   97.77   |   83.53   |
|    ×     |   √   |   √   |   5.6   |      4.8       |   95.54   |   93.06   |   94.21   |   97.35   |   83.58   |
|    √     |   √   |   √   | **5.4** |    **4.6**     | **96.19** | **96.07** | **96.11** | **98.36** | **87.81** |

Crucially, when all three modules were integrated, the model's performance showed a significant synergistic effect rather than a simple sum. The final YOLO-CEA model achieved an AP50-95 of **87.81%**, a remarkable improvement of **4.78 percentage points** over the baseline model. At the same time, the model's GFLOPs and size were reduced to **5.4** and **4.6MB**, respectively, making it the most efficient configuration of all. This fully demonstrates that the context-enhanced features from C3k2-HTC, the aligned and balanced features from ABFPN, and the high-reliability predictions from SLQMHead form a highly complementary system that achieves an overall performance optimization of the baseline model.

#### Comparative Experiments

Table 5 compares the comprehensive performance of YOLO-CEA with a series of mainstream object detection algorithms. The experimental results show that compared to two-stage detectors like Faster R-CNN, YOLO-CEA comprehensively surpasses them in accuracy, while its model size and computational load are far lower. In comparison with lightweight models from the YOLO series, YOLO-CEA also performs exceptionally well. For example, compared to the baseline model YOLOv11n, YOLO-CEA improved the AP50-95 score for flashover defects by 4.05 percentage points while reducing the computational load by 14.3%. It is particularly noteworthy that when compared with the latest MCI-GLA (2024 TIM) model, although the latter has an advantage in a single metric, YOLO-CEA leads in three key metrics: **Breakage AP50, Flashover AP50, and Flashover AP50-95**. Furthermore, its parameter count and computational load are only **5.5% and 5.1%** of MCI-GLA's, respectively.

|      Method       | Breakage AP50/% | Breakage AP50-95/% | Flashover AP50/% | Flashover AP50-95/% | Parameters(M) | GFLOPS | Weight Size(MB) |
| :---------------: | :-------------: | :----------------: | :--------------: | :-----------------: | ------------- | ------ | --------------- |
|   Faster R-CNN    |      90.8       |        57.7        |       88.8       |        65.8         | 41.75         | 134    | 159.5           |
|   Cascade R-CNN   |      91.8       |        64.2        |       91.0       |        72.0         | 69.39         | 162    | 265.0           |
|   Dynamic R-CNN   |      95.0       |        68.2        |       92.5       |        71.3         | 41.75         | 134    | 159.5           |
|    Libra R-CNN    |      91.2       |        60.5        |       87.9       |        67.7         | 42.02         | 136    | 160.5           |
|     RetinaNet     |      84.3       |        47.6        |       67.0       |        40.7         | 34.31         | 154.17 | 130.9           |
|    yolov3-tiny    |      97.37      |       79.89        |      97.35       |        87.13        | 12.12         | 18.9   | 23.3            |
|      yolov5n      |      96.58      |       77.43        |      98.30       |        87.08        | 2.50          | 7.1    | 5.0             |
|      yolov6n      |      95.30      |       78.32        |      97.71       |        87.04        | 4.23          | 11.8   | 8.3             |
|    YOLOv7-tiny    |      92.87      |       68.07        |      93.65       |        78.94        | 6.01          | 11.7   | 13.0            |
|      yolov8n      |      98.20      |       81.05        |      98.36       |        87.94        | 3.00          | 8.1    | 6.0             |
|      YOLOv9t      |      96.23      |       80.38        |      97.93       |        87.96        | 1.97          | 7.6    | 4.4             |
|     YOLOv10n      |      95.55      |       79.20        |      97.39       |        86.94        | 2.69          | 8.2    | 5.5             |
|  YOLOv11n(base)   |      96.36      |       77.00        |      97.45       |        86.63        | 2.58          | 6.3    | 5.2             |
|      YOLO12n      |      95.48      |       78.43        |      98.18       |        88.01        | 2.55          | 6.3    | 5.3             |
| MCI-GLA(2024 TIM) |      95.6       |      **85.7**      |       96.6       |        86.6         | 40.6          | 106.4  | 97.4            |
|  Proposed model   |    **97.76**    |       82.80        |    **98.58**     |      **90.68**      | 2.25          | 5.4    | 4.6             |

To further validate the generalization performance of the YOLO-CEA model, we evaluated it on another public dataset, CPLID, and compared it with several advanced models. The experimental results are shown in Table 6.

YOLO-CEA also demonstrated excellent performance on this dataset, achieving an mAP of 99.32% and a balanced performance in precision (P), recall (R), and F1-score. In terms of model efficiency, YOLO-CEA's parameter count (2.25M) and computational load (5.4 GFLOPs) remained the lowest among all compared models, once again proving the success of its lightweight design.

In terms of detection accuracy, YOLO-CEA's mAP (99.32%) was on par with YOLOv6n and slightly better than YOLOv8n (99.31%). Although SnakeNet and the improved YOLOv9 achieved higher mAP values, their model complexity also increased accordingly. In contrast, YOLO-CEA achieved a better balance between performance and resource consumption by ensuring extremely high detection accuracy while realizing superior efficiency.

In summary, the experimental results on the CPLID dataset once again confirm the high efficiency, high accuracy, and strong generalization ability of the YOLO-CEA model across different scenarios.

|      Method       | Parameter(M) | GFLOPS |   P   |   R   |  F1   |  mAP  |
| :---------------: | :----------: | :----: | :---: | :---: | :---: | :---: |
|    yolov3-tiny    |    12.12     |  18.9  | 97.60 | 89.40 | 93.14 | 92.81 |
|      yolov5n      |     2.50     |  7.1   | 96.48 | 98.81 | 97.63 | 99.18 |
|      yolov6n      |     4.23     |  11.8  | 98.26 | 98.68 | 98.47 | 99.32 |
|      YOLOv7t      |     6.01     |  13.0  | 96.93 | 99.01 | 97.95 | 98.69 |
|      yolov8n      |     3.00     |  8.1   | 98.21 | 97.74 | 97.97 | 99.31 |
|      YOLOv9t      |     1.97     |  7.6   | 96.97 | 98.87 | 97.91 | 99.27 |
|     YOLOv10n      |     2.69     |  8.2   | 94.99 | 96.40 | 95.65 | 98.58 |
|     YOLOv11n      |     2.58     |  6.3   | 97.39 | 98.68 | 98.03 | 99.08 |
|      YOLO12n      |     2.55     |  6.3   | 96.76 | 99.25 | 97.98 | 99.19 |
|    SnakeNet[]     |     2.90     |  6.70  | 99.70 | 99.80 |  94   | 99.50 |
| improved YOLOv9[] |     2.93     |  6.90  | 99.84 | 99.92 | 98.88 | 99.66 |
|  Proposed model   |     2.25     |  5.4   | 96.75 | 98.94 | 97.83 | 99.32 |

## CONCLUSION

To address the challenge of balancing accuracy and efficiency in insulator defect detection within complex power line scenarios, this paper proposes a novel detection framework named **Context-Enhanced and Aligned YOLO (YOLO-CEA)**. This framework systematically reconstructs the backbone, feature fusion neck, and detection head of the baseline model YOLOv11, aiming to enhance detection performance from three aspects: **1) enhancing global context awareness through the Transformer-fused C3k2-HTC module; 2) achieving precise and balanced multi-scale feature alignment through the AB-FPN, which employs soft interpolation (SNI) and efficient convolution (GSConvE); and 3) improving parameter efficiency and enhancing prediction reliability through the SharedLQMHead with its location quality modulation mechanism.**

Comprehensive experiments on the public IDID dataset fully validate the outstanding performance and sound design of YOLO-CEA. **Component validation experiments** show that the three innovative modules—C3k2-HTC, ABFPN, and SharedLQMHead—can each improve detection accuracy for specific categories while effectively reducing or maintaining the model's computational complexity. The **ablation study** further reveals a significant synergistic effect among the modules: when all three are integrated, the final YOLO-CEA model achieves an AP50-95 of **87.81%**, a substantial improvement of **4.78 percentage points** over the baseline. Critically, this performance leap is accompanied by a reduction in model complexity, with the final model's computational load (GFLOPS) and size (MB) decreasing to **5.4 GFLOPs** and **4.6 MB**, respectively, making it the most efficient configuration of all and proving the effectiveness of our design.

In the **comparative experiments** against mainstream algorithms, YOLO-CEA's advantages are even more pronounced. It not only comprehensively surpasses various classic YOLO variants in both accuracy and efficiency but also outperforms the latest MCI-GLA (2024 TIM) model, achieving leading scores in several key metrics for breakage and flashover defects with only **5.5% of its parameters and 5.1% of its computational load**. Furthermore, in tests on the CPLID dataset, YOLO-CEA also achieved a high mAP of **99.32%** while maintaining the lowest computational overhead among all compared models, strongly demonstrating its powerful **generalization ability** and robustness across different scenarios.

In conclusion, the YOLO-CEA model proposed in this study, through a synergistic design of context enhancement, feature alignment, and reliability modulation, successfully achieves an excellent balance between SOTA-level detection accuracy and extreme computational efficiency for the task of insulator defect detection. This provides a viable solution for developing lightweight, high-performance intelligent inspection systems, holding significant academic value and broad engineering application prospects. Despite these significant achievements, future work can be further deepened by expanding the dataset with more diverse and extreme operating conditions, as well as by conducting deployment validation on actual drone hardware.
