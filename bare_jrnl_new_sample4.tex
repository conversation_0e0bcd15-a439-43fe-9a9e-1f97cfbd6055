\documentclass[lettersize,journal]{IEEEtran}
\usepackage{amsmath,amsfonts}
\usepackage{algorithmic}
\usepackage{algorithm}
\usepackage{array}
\usepackage[caption=false,font=normalsize,labelfont=sf,textfont=sf]{subfig}
\usepackage{textcomp}
\usepackage{stfloats}
\usepackage{url}
\usepackage{verbatim}
\usepackage{graphicx}
\usepackage{cite}
\hyphenation{op-tical net-works semi-conduc-tor IEEE-Xplore}
% updated with editorial comments 8/9/2021

\begin{document}

\title{YOLO-CEA: Context-Enhanced and Aligned Algorithm for Insulator Defect Detection in Power Transmission Lines}

\author{Your Name,~\IEEEmembership{Member,~IEEE,}
        % <-this % stops a space
\thanks{This paper was produced for insulator defect detection research.}% <-this % stops a space
\thanks{Manuscript received [Date]; revised [Date].}}

% The paper headers
\markboth{IEEE Transactions on [Journal Name],~Vol.~XX, No.~X, [Month]~[Year]}%
{Author \MakeLowercase{\textit{et al.}}: YOLO-CEA for Insulator Defect Detection}

\IEEEpubid{0000--0000/00\$00.00~\copyright~[Year] IEEE}
% Remember, if you use this you must call \IEEEpubidadjcol in the second
% column for its text to clear the IEEEpubid mark.

\maketitle

\begin{abstract}
Accurate and efficient detection of insulator defects is crucial for ensuring the stability and safety of power transmission systems. However, this task is challenged by complex backgrounds, significant scale variations of defects, and the strict computational constraints of real-time inspection platforms like unmanned aerial vehicles (UAVs). To address these challenges, this paper introduces YOLO-CEA (Context-Enhanced and Aligned YOLO), a novel, lightweight, and high-performance detection framework systematically re-engineered from YOLOv11. YOLO-CEA integrates three core innovations: 1) A \textbf{C3k2-HTC} module in the backbone, which synergistically fuses Transformer-based global context with CNN-based local features to enhance feature representation in complex scenes. 2) An \textbf{Aligned-Balanced Feature Pyramid Network (AB-FPN)} in the neck, which employs Soft Nearest Neighbor Interpolation (SNI) and GSConv Enhancement (GSConvE) to achieve precise multi-scale feature alignment and an optimal balance between computational cost and information richness. 3) A \textbf{Shared Location Quality Modulation Head (SharedLQMHead)}, which improves parameter efficiency and introduces a novel mechanism to align classification confidence with localization accuracy, thereby enhancing the reliability of detection results. Experimental results on the public IDID dataset demonstrate that YOLO-CEA achieves an AP50-95 of 87.81\%, a significant \textbf{4.78 percentage point improvement} over the baseline, while simultaneously reducing GFLOPs to \textbf{5.4} and model size to \textbf{4.6 MB}. Notably, YOLO-CEA surpasses recent heavyweight models in key metrics with only a fraction of their computational cost, and extensive tests on the CPLID dataset further validate its superior performance and strong generalization ability. The proposed model achieves a state-of-the-art balance between detection accuracy and computational efficiency, demonstrating significant potential for deployment in automated, resource-constrained aerial inspection systems.
\end{abstract}

\begin{IEEEkeywords}
Insulator Defect Detection, YOLO, Lightweight Object Detection, Feature Fusion, Attention Mechanism, Transformer.
\end{IEEEkeywords}

\section{Introduction}
\IEEEPARstart{I}{nsulators} are fundamental components of high-voltage transmission lines, providing essential electrical insulation and mechanical support. However, their prolonged exposure to harsh environmental conditions and continuous electrical stress inevitably leads to defects such as breakage, contamination flashover, and self-shattering \cite{ref1}. These defects critically compromise the insulation integrity of the power system, potentially triggering large-scale outages and incurring significant economic losses \cite{ref2}. Consequently, the regular and accurate inspection of insulators has become a critical task for ensuring the safe and reliable operation of the power grid. While traditional manual inspections are fraught with high costs, low efficiency, and significant safety risks, the advent of Unmanned Aerial Vehicles (UAVs) equipped with high-resolution cameras has emerged as a transformative alternative, enabling safer, faster, and more comprehensive data acquisition \cite{ref3,ref4}. This technological shift has catalyzed the development of deep learning-based algorithms for the automated analysis of vast aerial imagery datasets.

Despite considerable progress, the automated detection of insulator defects from UAV imagery remains a formidable challenge, primarily due to three persistent issues. First, insulator targets are often situated against \textbf{highly complex and cluttered backgrounds}, including sky, terrain, vegetation, and intricate pylon structures, which can introduce significant noise and lead to false positives \cite{ref5}. Second, the defects themselves exhibit \textbf{significant scale variation}, ranging from large, conspicuous breakage to minute, subtle cracks. This demands that a detection model possess robust multi-scale perception capabilities to identify defects across a wide spectrum of sizes \cite{ref6}. Third, for practical field deployment on UAVs, the detection models must be \textbf{lightweight and computationally efficient} to adhere to the stringent constraints of on-board processing, such as limited memory, low power consumption, and the need for real-time inference \cite{ref7}. This creates a fundamental tension between achieving high detection accuracy and maintaining low model complexity.

Many existing object detection models struggle to strike an optimal balance for this specific task. Two-stage detectors like Faster R-CNN \cite{ref8}, while often accurate, are typically too computationally intensive and slow for real-time applications on edge devices. Single-stage detectors from the YOLO (You Only Look Once) series have gained popularity due to their superior trade-off between speed and accuracy \cite{ref9}. However, even state-of-the-art YOLO models exhibit inherent limitations when applied to insulator defect detection. Firstly, their standard backbone networks, built upon convolutional neural networks (CNNs), possess an intrinsically limited receptive field, hampering their ability to capture the \textbf{global contextual information} needed to distinguish small defects from complex background textures \cite{ref10}. Secondly, their feature pyramid networks (FPNs) \cite{ref11}, used for multi-scale feature fusion, often rely on simple interpolation methods that can cause \textbf{feature misalignment}, degrading the quality of fused features and impairing the detection of objects at disparate scales. Finally, their detection heads typically treat classification and localization as decoupled tasks, often resulting in a \textbf{misalignment between high classification confidence and poor localization quality}, which reduces the overall reliability of the final predictions \cite{ref12}.

To overcome these multifaceted limitations, this paper proposes a novel lightweight and high-performance detector named \textbf{Context-Enhanced and Aligned YOLO (YOLO-CEA)}. Our approach systematically innovates upon the architecture's three core components—backbone, neck, and head—to specifically address the challenges of insulator defect detection. The main contributions of this work are summarized as follows:

\begin{enumerate}
\item We propose a \textbf{Context-Enhanced Backbone} incorporating a novel \textbf{C3k2-HTC module}. This hybrid block synergistically integrates a Transformer with a CNN, enabling the model to capture both long-range dependencies and fine-grained local features. This significantly enhances feature representation, especially in cluttered background scenarios.

\item We design an \textbf{Aligned-Balanced Feature Pyramid Network (AB-FPN)} for the neck. This novel FPN replaces conventional operators with \textbf{Soft Nearest Neighbor Interpolation (SNI)} to mitigate feature misalignment during upsampling and employs \textbf{GSConv Enhancement (GSConvE)} for an efficient yet information-rich downsampling path, leading to superior fusion of multi-scale features.

\item We introduce a \textbf{Reliable and Efficient Head}, named \textbf{SharedLQMHead}. It utilizes cross-scale parameter sharing to reduce model complexity while integrating a novel \textbf{Location Quality Modulation (LQM)} mechanism. This mechanism explicitly aligns classification scores with localization accuracy, ensuring that the model yields highly reliable detections.

\item We conduct extensive experiments on the public IDID and CPLID datasets. The results demonstrate that our proposed YOLO-CEA not only achieves state-of-the-art detection accuracy, significantly outperforming the baseline and other competing models, but also maintains exceptional computational efficiency, establishing a new benchmark for lightweight insulator defect detection.
\end{enumerate}


\section{Related Work}

The field of insulator defect detection has evolved significantly, transitioning from manual inspection to sophisticated automated systems. This section reviews the trajectory of these methods, analyzing their respective strengths and limitations to contextualize the contributions of our proposed work.

Initial approaches to insulator inspection relied on manual patrols, a practice known to be labor-intensive, inefficient, and fraught with safety risks for personnel, particularly in challenging geographical and environmental conditions \cite{ref10}. To mitigate these issues, early research explored automation through traditional computer vision techniques. These methods often utilized handcrafted features, such as directional angles and shape priors for localization \cite{ref11} or local feature descriptors for classification \cite{ref12}. However, their performance was inherently fragile, showing limited robustness to the vast variations in lighting, viewing angles, and background clutter encountered in real-world aerial imagery, thus hindering their practical deployment.

The paradigm shifted with the advent of deep learning, especially with the success of Convolutional Neural Networks (CNNs), which have demonstrated superior performance in object detection. Within this domain, methods are broadly categorized as two-stage or one-stage. Two-stage detectors, such as the R-CNN family \cite{ref13,ref14,ref15}, first generate a set of candidate region proposals and then classify each proposal. Several studies have successfully adapted these models for insulator detection, integrating components like Feature Pyramid Networks (FPN) \cite{ref16} or Transformer-based attention mechanisms \cite{ref18} to achieve high precision. Despite their accuracy, these models are characterized by high computational complexity and slow inference speeds, rendering them impractical for real-time applications on resource-constrained edge devices like UAVs \cite{ref19}.

Consequently, one-stage detectors, particularly the You Only Look Once (YOLO) series \cite{ref20,ref21,ref22,ref23,ref24,ref25,ref26,ref27,ref28}, have become the predominant choice for this task due to their excellent trade-off between speed and accuracy. Research in this area has largely focused on enhancing the standard YOLO architecture to address key challenges. To tackle the issue of significant \textbf{scale variation} in defects, some researchers have modified the network's detection head, for instance, by replacing it with a Transformer-based prediction head \cite{ref14} or by adding an extra detection head for micro-scale targets \cite{ref15}. While beneficial, these modifications often overlook the critical problem of \textbf{feature misalignment} caused by naive upsampling operations in the feature fusion process. To handle \textbf{complex backgrounds}, the integration of attention mechanisms like Coordinate Attention (CA) \cite{ref16,ref17} or CBAM \cite{ref11,ref19} has been a popular strategy. These modules help the network focus on salient object features, yet they predominantly capture \textbf{local correlations} and often fail to model the \textbf{long-range, global contextual information} necessary to distinguish small defects from similarly textured background noise. Furthermore, for practical \textbf{lightweight deployment}, many have explored replacing standard convolutions with more efficient alternatives like GhostConv \cite{ref36,ref39} or depth-wise separable convolutions \cite{ref38}. While effective in reducing model parameters and FLOPs, this often comes at the cost of a noticeable drop in detection accuracy, creating a persistent trade-off.

Our review of the existing literature reveals a significant research gap. Current approaches tend to offer piecemeal solutions, addressing challenges like scale, background, or model complexity in isolation. There is a lack of a \textbf{holistic and systematic framework} that introduces coordinated innovations across all core components of the detector—backbone, neck, and head. This fragmented approach can lead to suboptimal performance, as enhancements in one part of the network may be bottlenecked by limitations in another. For example, rich features extracted by a powerful backbone can be degraded by a misaligned feature pyramid, or a well-localized bounding box can be wrongly suppressed due to an unreliable classification score. To bridge this gap, this paper proposes \textbf{YOLO-CEA}, a detector designed with synergistic enhancements. By introducing a context-aware backbone for robust feature extraction, an alignment-focused neck for superior multi-scale fusion, and a reliability-oriented head that synchronizes classification and localization, our work aims to establish a new state-of-the-art balance between accuracy, efficiency, and reliability for insulator defect detection.





\section{Method}

This section elaborates on the novel framework we designed to enhance the performance of insulator defect detection. This framework systematically reconstructs and optimizes the backbone, feature fusion neck, and detection head of YOLOv11. We have named our new model \textbf{Context-Enhanced and Aligned YOLO (YOLO-CEA)}, and its overall architecture is shown in Figure~\ref{fig_architecture}. The core improvements of YOLO-CEA focus on three aspects: \textbf{feature context enhancement} through the introduction of a hybrid attention mechanism, \textbf{multi-scale feature alignment} by optimizing sampling strategies, and \textbf{improving the reliability of detection results} by refining the prediction head.

\begin{figure}[!t]
\centering
\includegraphics[width=3.5in]{fig1}
\caption{Overall architecture of the proposed YOLO-CEA model showing the three main components: Context-Enhanced Backbone with C3k2-HTC module, Aligned-Balanced Feature Pyramid Network (AB-FPN), and Shared Location Quality Modulation Head (SharedLQMHead).}
\label{fig_architecture}
\end{figure}

As shown in Figure~\ref{fig_architecture}, in the \textbf{Backbone} section, the input image first passes through a series of convolutional layers for downsampling to progressively extract multi-level feature maps. To fundamentally enhance the model's feature representation capabilities, we replaced the original C3k2 module in the deep layers of the backbone with our C3k2-HTC module. The C3k2-HTC module synergistically integrates the global context-capturing capabilities of Transformers with the fine-grained local feature extraction capabilities of convolutions, which is crucial for accurately identifying insulator defects of various morphologies in complex backgrounds. Subsequently, the SPPF module enhances the receptive field of features through multi-scale pooling operations, while the C2PSA module at the end of the backbone further strengthens the capture of key multi-scale information through its unique attention mechanism. This provides a richer and higher-quality feature foundation for the subsequent feature fusion network.

Next, these multi-scale features extracted from the backbone are fed into our designed \textbf{Aligned and Balanced Feature Pyramid Network (AB-FPN)} for feature fusion. AB-FPN ensures efficient interaction between features at different levels through two specially optimized paths (top-down and bottom-up). In the top-down path, we use the SNI module for upsampling. It employs an energy-aware soft interpolation method to effectively prevent high-level semantic features from excessively suppressing low-level detail features, thereby achieving precise feature alignment. In the bottom-up path, we use the GSConvE module for downsampling. This module significantly reduces computational costs while preserving rich feature information, achieving a perfect balance between computational efficiency and representational power. Through the synergistic action of SNI and GSConvE, AB-FPN generates multi-scale fused features that are better aligned and more information-balanced.

Finally, these fused features are passed to the \textbf{Shared Location Quality Modulation Detection Head (SharedLQMHead)} for final bounding box regression and class prediction. This detection head is designed to address the core issues of parameter redundancy and the mismatch between classification confidence and localization precision in traditional detectors. It first enhances parameter efficiency through a convolutional layer shared across scales. Then, it utilizes the innovative LQM module to dynamically adjust and calibrate classification scores based on the quality of the predicted bounding box probability distribution. This mechanism ensures that only prediction boxes with both high classification confidence and high localization accuracy receive a final high score, thereby significantly improving the reliability of the detection.

Through the coordinated operation of the above modules, YOLO-CEA demonstrates outstanding detection performance and efficiency in complex power line inspection scenarios. In the following subsections, we will provide an in-depth introduction to the design principles and implementation details of the C3k2-HTC module, the AB-FPN architecture, and the SharedLQMHead.


 

\subsection{C3k2-HTC Module}

To effectively address the challenges of insulator defect detection in the complex backgrounds of power transmission lines, this paper optimizes the feature extraction capability of the YOLOv11 backbone. In the deep stages of the original YOLOv11 backbone, the model primarily relies on the C3k2 module for feature extraction. The core computational unit of the C3k2 module consists of multiple cascaded Bottleneck or C3k modules, which is a purely convolution-based design. Although this structure is efficient at capturing local textures and spatial features, its capabilities reveal significant shortcomings when dealing with complex scenes that require global contextual information. Due to the fixed size of convolutional kernels, computation is limited to a local receptive field, making it difficult to establish dependencies between distant pixels in an image. For example, when determining whether a tiny object is a genuine insulator self-explosion defect or complex background noise, the model needs to not only identify its local features but also understand its global position within the entire insulator string and even the transmission tower. The pure-convolution C3k2 module is inadequate in this regard, easily leading to missed detections or false positives. To this end, this paper innovatively designs a \textbf{C3k2-HTC module (C3k2 module with Hybrid Transformer Conv Block)}, which aims to organically combine the powerful global modeling capabilities of Transformers with the efficient local feature extraction of CNNs, thereby fundamentally compensating for the network's deficiency in global information perception.

C3k2-HTC replaces the C3k in C3k2 with the \textbf{HTCBlock (Hybrid Transformer Conv Block)}. The HTCBlock is key to achieving parallel processing of local and global information. It first divides the channels of the input feature map according to an adjustable mixing ratio hyperparameter $r$. A proportion $r$ of the channels is sent to a \textbf{TransformerBlock} branch. This branch not only includes a standard Multi-Head Self-Attention (MHSA) module to capture long-range dependencies, but its Feed-Forward Network (FFN) part has also been critically optimized. Compared to the conventional design in Vision Transformers that uses simple linear layers and GELU activation functions, we introduce the \textbf{Gated Linear Unit (CGLU)}. CGLU uses a dynamic, data-driven gating mechanism to regulate information flow, replacing the fixed activation function. This endows the FFN with stronger expressive power and non-linear modeling potential, allowing it to process and transform features more adaptively, thereby further improving the quality of extracted global contextual information. Meanwhile, the remaining proportion $1-r$ of channels enters a standard \textbf{Bottleneck} convolution branch to preserve and refine local spatial details and texture features. The outputs of these two branches are concatenated along the channel dimension and deeply fused through a $1 \times 1$ convolutional layer. This parallel hybrid structure enables the model to perceive both local details and global context within a single module, achieving a complementary advantage between local and global information.

In the overall design of C3k2-HTC, the input features first pass through a $1 \times 1$ convolution and are then split into two paths. One path serves as a direct shortcut connection, while the other is sent into a sequence of multiple cascaded HTCBlocks for deep feature extraction. Finally, the features from these two paths are concatenated and fused, a structure that greatly facilitates gradient flow and cross-layer feature reuse.

Considering that deep network layers process more high-level, abstract semantic features, establishing global dependencies on these features is more meaningful than on shallow-level, low-level texture features. Furthermore, the computational complexity of the self-attention mechanism in Transformers is quadratic with respect to the feature map size. Applying it in the deep layers of the network, where the spatial resolution is already significantly reduced, allows for efficient global context modeling at a controllable computational cost. Therefore, we deploy this module in the deep stages of the backbone to replace the original C3k2 module, striking a balance between model performance and efficiency.

\subsection{Aligned-Balanced Feature Pyramid Network (AB-FPN)}

YOLOv11 employs the classic Path Aggregation Network (PANet) as its neck structure, achieving effective multi-scale feature fusion through top-down and bottom-up pathways. However, when dealing with targets like insulator defects, which exhibit vast size variations and are rich in detailed information, we found that the feature fusion mechanism of the original YOLOv11 neck still has room for optimization. Specifically, its top-down path uses \textbf{standard nearest-neighbor interpolation}, and its bottom-up path uses \textbf{standard strided convolution}, which can lead to \textbf{feature misalignment} and \textbf{computational redundancy}, respectively.

To address these challenges, we have deeply optimized the neck of YOLOv11 and proposed a new feature fusion architecture—the \textbf{Aligned-Balanced Feature Pyramid Network (AB-FPN)}. The core idea of AB-FPN is to replace the original upsampling and downsampling operations with two specially designed lightweight modules, namely \textbf{Soft Nearest Neighbor Interpolation (SNI)} and \textbf{GSConv Enhancement (GSConvE)}. This achieves better feature alignment and computational balance with almost no additional cost.

In the original top-down path of YOLOv11, high-level feature maps are enlarged using standard nearest-neighbor interpolation. This "hard" pixel replication causes the energy (sum of activation values) of the feature map to grow quadratically with the magnification factor, leading to high-level semantic features dominating the fusion with low-level detail features. This imbalanced fusion can easily cause feature misalignment, which is particularly detrimental to preserving weak features such as tiny cracks on insulators. The SNI module aims to "soften" this process. After performing nearest-neighbor interpolation, it introduces a softening coefficient $\alpha$, which is related to the scaling factor, to normalize the energy of the result. Its operation can be defined by Equation~\eqref{eq:sni}:

\begin{equation}
\label{eq:sni}
\mathbf{Y}= \alpha \cdot f_{\text{nearest}}(\mathbf{X}) \quad \text{where} \quad \alpha = \frac{1}{k^2}
\end{equation}
where $\mathbf{X}$ is the deep feature map to be upsampled, $\mathbf{Y}$ is the upsampled result, $f_{\text{nearest}}$ represents the nearest-neighbor interpolation operation, and $k$ is the upsampling factor.

By introducing $\alpha$, SNI \textbf{regulates} the weights of features from different levels during fusion, effectively mitigating the drastic energy changes caused by upsampling and preventing high-level features from excessively "covering" low-level features. This makes the fusion process smoother, promotes \textbf{Secondary Features Alignment}, and significantly reduces the difficulty for subsequent convolutional layers to learn the fused representation, thereby enhancing the model's comprehensive perception of multi-scale defects.

In the original bottom-up path of YOLOv11, feature map downsampling is accomplished by a $3\times3$ convolution with a stride of 2. While effective, standard convolution has a relatively large number of parameters and computational load (FLOPs) when processing high-channel feature maps, presenting an optimization opportunity for detection models that pursue ultimate efficiency.

To reduce computational cost while maintaining or even enhancing feature representation capabilities, we employ the GSConvE module to perform the downsampling task. GSConvE is an efficient hybrid convolution structure, with its core idea being to strike a delicate balance between computational efficiency and feature richness. GSConvE cleverly decomposes the feature processing into two parallel branches. First, the input feature map flows through a standard convolutional layer, which reduces the number of input channels to half of the target number of channels. This step is responsible for extracting information-dense "base features" with cross-channel interactions. Subsequently, these generated "base features" are sent to an extremely lightweight secondary processing branch. This branch consists of a 2D convolution, a depth-wise separable convolution (DWC), and a GELU activation function in series. Since the computational cost of depth-wise separable convolution is extremely low, this branch can generate "enhanced features" with diverse receptive fields and texture information with almost no additional burden. Finally, the "base features" from the dense path and the "enhanced features" from the sparse path are concatenated along the channel dimension. To break the channel-wise separation of features from the two paths and promote full information interaction, a crucial \textbf{Channel Shuffle} operation is applied to the concatenated feature map. This operation efficiently recombines features from different computational paths, ensuring that information is thoroughly mixed before being passed to the next layer.

Essentially, GSConvE adopts a "divide and conquer" strategy: it lets the computationally expensive standard convolution focus on extracting the most critical cross-channel information, while assigning the task of generating feature diversity to the extremely low-cost depth-wise convolution. Compared to the single standard convolution in the original YOLOv11, this design achieves an excellent \textbf{balance} between \textbf{computational efficiency} (from sparse depth-wise convolution) and \textbf{feature richness} (from dense standard convolution). It can provide the model with stronger representation capabilities while significantly reducing the number of parameters and computational load, thereby improving the efficiency of feature aggregation in the bottom-up path.

In summary, our proposed AB-FPN architecture performs targeted optimizations on the key components of the original YOLOv11 neck through SNI and GSConvE. It systematically enhances the performance of the feature pyramid from the dimensions of "alignment" and "balance," laying a solid foundation for high-precision, high-efficiency insulator defect detection.
\subsection{Shared Location Quality Modulation Detection Head (SharedLQMHead)}

Detection heads in modern object detectors generally face two interrelated challenges: first, \textbf{parameter efficiency}, which refers to the parameter redundancy and computational overhead caused by deploying independent prediction branches for each level of the multi-scale feature pyramid; and second, \textbf{prediction reliability}, which is the problem of a lack of effective information interaction between the parallel tasks of classification and localization, leading to inconsistency between classification confidence and localization precision. To systematically address these challenges, we propose a novel detection head architecture—the \textbf{Shared Location Quality Modulation Detection Head (SharedLQMHead)}. This architecture collaboratively optimizes parameter efficiency and prediction reliability through a unified design.

The core design of SharedLQMHead includes two interrelated mechanisms: \textbf{cross-scale parameter sharing} and \textbf{location quality modulation}.

First, to improve parameter efficiency and enhance the generalization capability of feature representations, we introduce a \textbf{cross-scale parameter sharing mechanism}. Unlike traditional designs that equip each FPN level with an independent convolutional layer, SharedLQMHead inputs feature maps from all scales into a \textbf{unified, parameter-shared convolutional module}. This design not only significantly reduces the number of parameters in the detection head but, more importantly, it forces the network to use the same set of transformation kernels across different scales, prompting the model to learn a scale-invariant feature extraction paradigm. The resulting shared feature representation has stronger generalization ability and provides high-quality input for the subsequent classification and regression tasks.

Second, based on these efficient shared features, we introduce the \textbf{Location Quality Modulation (LQM) mechanism} to address the inconsistency between classification and regression. This mechanism stems from a core insight: the probability distribution shape of bounding box regression can reflect its localization certainty. An accurate localization prediction should have a low-entropy discrete probability distribution $P_{box}$, meaning the distribution is sharp and concentrated. Conversely, an uncertain prediction corresponds to a high-entropy, flat distribution. LQM uses a lightweight multi-layer perceptron ($\mathcal{M}_{LQM}$) to map statistical measures extracted from $P_{box}$ (such as the mean of the top-k probability values) into a scalar score, which we call the \textbf{location quality score} ($S_{qual}$). Its calculation can be formalized as:
\begin{equation}
S_{qual} = \mathcal{M}_{LQM}(\text{Stat}(P_{box}))
\end{equation}
This $S_{qual}$ score serves as an explicit quantification of localization quality and is used to modulate the initial classification score $S_{cls}$. The final confidence score $S_{final}$ is formed by their additive fusion:
\begin{equation}
S_{final} = S_{cls} + S_{qual}
\end{equation}
In this way, localization quality is explicitly integrated into the final confidence assessment. For a prediction to achieve high confidence, it must satisfy both a high classification score and high localization quality. This mechanismically promotes synergy between the classification and regression tasks, enhancing the reliability of the final detection results.

In summary, the parameter sharing mechanism of SharedLQMHead first builds an efficient and generalizable feature extraction foundation, while the location quality modulation mechanism further calibrates the reliability of predictions on this basis. The organic combination of these two components forms a unified detection framework that is significantly improved in both efficiency and reliability.

\section{Experimental}

\subsection{Dataset}

This study selected two widely recognized public datasets: the Chinese Power Line Insulator Dataset (CPLID) \cite{ref22} and the Insulator Defect Image Dataset (IDID). The IDID dataset focuses on insulator strings on power transmission lines, where the main objects in the images are insulator bodies, classified into three categories based on their surface condition: \textbf{Good insulator shell}, \textbf{Broken insulator shell}, and \textbf{Flashover damage insulator shell}. It contains \textbf{1596 images} and \textbf{7568 annotated objects}, specifically including 1788 insulator strings, 2636 good shells, 1140 broken shells, and 2004 flashover shells. The CPLID dataset consists of 848 images, including 600 images of normal insulators and 248 images of Defective\_Insulators. A notable feature of these defect images is that they are all artificially synthesized by manually pasting defective insulators onto different background images. Each dataset was split into training and validation sets at an 8:2 ratio.

\subsection{Experimental Setup}

All experiments in this study were conducted on the same high-performance server, with consistent hardware and software environments to eliminate interference from computational platform differences on the experimental results. The core hardware for the experiments included an Intel Core i9-13900KF processor and an NVIDIA GeForce RTX 4090 graphics processor, the latter providing powerful parallel computing support for model training. On the software side, we implemented the algorithms based on Python 3.10 and the PyTorch 2.2.2 framework, and used the CUDA 12.1 library for GPU acceleration. The specific configuration information is shown in Table~\ref{tab:hardware}.

\begin{table}[!t]
\caption{Hardware and Software Configuration for the Experiments\label{tab:hardware}}
\centering
\begin{tabular}{|l|l|}
\hline
\textbf{Item} & \textbf{Value} \\
\hline
OS & Ubuntu 20.04 LTS \\
\hline
CPU & 13th Gen Intel(R) Core(TM) i9-13900KF \\
\hline
GPU & NVIDIA GeForce RTX 4090 \\
\hline
Python & 3.10 \\
\hline
CUDA & 12.1 \\
\hline
PyTorch & 2.2.2 \\
\hline
\end{tabular}
\end{table}

In the implementation of model training, we unified key hyperparameter settings to ensure fair comparison. We used the Stochastic Gradient Descent (SGD) optimizer for end-to-end model training. The initial learning rate was set to 0.01, the momentum factor was 0.937, and a weight decay coefficient of 0.0005 was used to suppress overfitting. The entire training process consisted of 300 epochs. To improve data processing efficiency, we set the batch size to 16 and enabled 4 workers for parallel data loading. The detailed settings of all key hyperparameters are summarized in Table~\ref{tab:params}.

\begin{table}[!t]
\caption{Experimental Parameters\label{tab:params}}
\centering
\begin{tabular}{|l|l|}
\hline
\textbf{Item} & \textbf{Value} \\
\hline
Optimizer & SGD \\
\hline
Learning Rate & 0.01 \\
\hline
Epochs & 300 \\
\hline
Momentum & 0.937 \\
\hline
Batch Size & 16 \\
\hline
Workers & 4 \\
\hline
Weight Decay Factor & 0.0005 \\
\hline
\end{tabular}
\end{table}





\subsection{Evaluation Metrics}

The model's performance is evaluated based on two key aspects: computational efficiency and detection accuracy. Model efficiency and complexity are assessed using three metrics. \textbf{Parameters} (M) represent the total number of learnable weights and biases, indicating the model's scale and memory footprint. \textbf{Model Size} (MB) is the disk space required to store the trained weights, a direct measure of storage requirements. \textbf{GFLOPs} (Giga Floating-point Operations) quantifies the computational workload for a single forward pass; a lower value signifies higher efficiency, which is critical for real-time deployment on resource-constrained devices. Detection accuracy is assessed through a hierarchy of metrics built upon the concepts of True Positives (TP), False Positives (FP), and False Negatives (FN). From these, we define \textbf{Precision}, the ratio of correct detections to all positive predictions, and \textbf{Recall}, the ratio of correct detections to all ground-truth objects.

\begin{equation}
\text{Precision} = \frac{\text{TP}}{\text{TP} + \text{FP}}
\end{equation}

\begin{equation}
\text{Recall} = \frac{\text{TP}}{\text{TP} + \text{FN}}
\end{equation}

To balance the inherent trade-off between these two, the \textbf{F1-score} is calculated as their harmonic mean:
\begin{equation}
\text{F1} = 2 \times \frac{\text{Precision} \times \text{Recall}}{\text{Precision} + \text{Recall}}
\end{equation}
For a more comprehensive single-class evaluation, \textbf{Average Precision (AP)} is used. Calculated as the area under the Precision-Recall (P-R) curve, AP summarizes performance across all confidence thresholds.
\begin{equation}
\text{AP} = \int_{0}^{1} p(r) dr
\end{equation}
Finally, the \textbf{mean Average Precision (mAP)} serves as the primary overall metric, assessing performance across all $N$ classes by averaging their individual AP scores.
\begin{equation}
\text{mAP} = \frac{1}{N} \sum_{i=1}^{N} \text{AP}_i
\end{equation}

\subsection{Experimental Results Analysis}

To comprehensively evaluate the performance of the proposed Context-Enhanced and Aligned YOLO (YOLO-CEA) model for insulator defect detection, we conducted a series of experiments on the public Insulator Defect Image Dataset (IDID). We first performed independent component validation for the core modules—C3k2\_HTC, ABFPN, and SLQMHead—then examined the synergistic effects of each module through ablation studies, and finally compared the overall performance of our model with current mainstream object detection algorithms.

\subsubsection{Experimental Validation and Analysis of the C3k2 Module}

In the backbone network, our designed \textbf{C3k2-HTC module} integrates the global context-aware capabilities of Transformers with the local detail extraction capabilities of CNNs, aiming to enhance the model's ability to represent defect features in complex backgrounds. As shown in Table~\ref{tab:c3k2_results}, compared to the baseline C3k2, C3k2-HTC improved the AP50 scores for breakage and flashover defects by 1.42\% and 0.70\%, reaching 97.78\% and 98.15\%, respectively. This result confirms that by processing local and global information in parallel and enhancing the feed-forward network with a Gated Linear Unit (CGLU), C3k2-HTC can extract more discriminative features. Notably, this performance gain was accompanied by a reduction in parameters and computational load (from 2.58M/6.3 GFLOPs to 2.45M/6.2 GFLOPs), fully demonstrating the efficiency and effectiveness of its design.

\begin{table}[!t]
\caption{Performance Comparison of C3k2 Module Variants\label{tab:c3k2_results}}
\centering
\footnotesize
\begin{tabular}{|l|c|c|c|c|c|c|c|}
\hline
\textbf{Method} & \textbf{Breakage} & \textbf{Breakage} & \textbf{Flashover} & \textbf{Flashover} & \textbf{Parameters} & \textbf{GFLOPs} & \textbf{Weight Size} \\
 & \textbf{AP50/\%} & \textbf{AP50-95/\%} & \textbf{AP50/\%} & \textbf{AP50-95/\%} & \textbf{(M)} &  & \textbf{(MB)} \\
\hline
C3k2 & 96.36 & 77.00 & 97.45 & 86.63 & 2.58 & 6.3 & 5.2 \\
\hline
C3k2-faster & 91.01 & 70.30 & 95.72 & 82.30 & 2.28 & 5.8 & 4.7 \\
\hline
C3k2-star & 95.08 & 77.06 & 97.44 & 86.77 & 2.47 & 6.4 & 5.1 \\
\hline
C3k2\_HTC & \textbf{97.78} & \textbf{80.41} & \textbf{98.15} & \textbf{86.84} & \textbf{2.45} & \textbf{6.2} & \textbf{5.0} \\
\hline
\end{tabular}
\end{table}

\subsubsection{Experimental Validation and Analysis of FPN}

In the feature fusion neck, our designed \textbf{Aligned and Balanced Feature Pyramid Network (ABFPN)} demonstrated a significant advantage in multi-scale feature processing. According to the data in Table~\ref{tab:fpn_results}, ABFPN increased the AP50-95 for breakage defects from 77.00\% to 80.85\% with almost no increase in computational cost (6.3 GFLOPs), outperforming other FPN variants in overall performance. This is mainly attributed to its internal mechanisms: first, the SNI module in the top-down path effectively mitigates feature misalignment during upsampling through energy-aware soft interpolation; second, the GSConvE module in the bottom-up path reduces computational overhead while preserving rich feature information. Ultimately, ABFPN generates better-aligned and more information-balanced multi-scale feature maps, laying a solid foundation for subsequent accurate detection.

\begin{table}[!t]
\caption{Performance Comparison of FPN Variants\label{tab:fpn_results}}
\centering
\footnotesize
\begin{tabular}{|l|c|c|c|c|c|c|c|}
\hline
\textbf{Method} & \textbf{Breakage} & \textbf{Breakage} & \textbf{Flashover} & \textbf{Flashover} & \textbf{Parameters} & \textbf{GFLOPs} & \textbf{Weight Size} \\
 & \textbf{AP50/\%} & \textbf{AP50-95/\%} & \textbf{AP50/\%} & \textbf{AP50-95/\%} & \textbf{(M)} &  & \textbf{(MB)} \\
\hline
FPN-PAN & 96.36 & 77.00 & 97.45 & 86.63 & 2.58 & 6.3 & 5.2 \\
\hline
AFPN & 96.63 & 78.96 & 98.42 & 88.46 & 2.65 & 8.8 & 5.5 \\
\hline
BiFPN & 96.13 & 78.53 & 98.09 & 86.30 & 1.92 & 6.3 & 4.0 \\
\hline
ABFPN & \textbf{97.85} & \textbf{80.85} & \textbf{97.87} & \textbf{87.06} & \textbf{2.53} & \textbf{6.3} & \textbf{5.2} \\
\hline
\end{tabular}
\end{table}

\subsubsection{Experimental Validation and Analysis of the Head}

At the detection head level, our proposed \textbf{Shared Location Quality Modulation Detection Head (SLQMHead)} aims to improve prediction reliability. As shown in Table~\ref{tab:head_results}, this detection head achieved the highest AP50 of 98.36\% for flashover defect detection while significantly reducing the model's computational complexity (5.6 GFLOPs) and parameter count. This validates the effectiveness of its core LQM module, which dynamically calibrates classification confidence by evaluating the quality of localization predictions. This mechanism fundamentally promotes synergy between classification and regression tasks, ensuring the high reliability of detection results in terms of both class and location.

\begin{table}[!t]
\caption{Performance Comparison of Detection Head Variants\label{tab:head_results}}
\centering
\footnotesize
\begin{tabular}{|l|c|c|c|c|c|c|c|}
\hline
\textbf{Method} & \textbf{Breakage} & \textbf{Breakage} & \textbf{Flashover} & \textbf{Flashover} & \textbf{Parameters} & \textbf{GFLOPs} & \textbf{Weight Size} \\
 & \textbf{AP50/\%} & \textbf{AP50-95/\%} & \textbf{AP50/\%} & \textbf{AP50-95/\%} & \textbf{(M)} &  & \textbf{(MB)} \\
\hline
Head & 96.36 & 77.00 & 97.45 & 86.63 & 2.58 & 6.3 & 5.2 \\
\hline
LSCD & 97.80 & 77.64 & 97.59 & 87.18 & 2.42 & 5.6 & 4.9 \\
\hline
LSCSBD & 97.40 & 80.66 & 97.80 & 87.99 & 2.45 & 6.2 & 5.0 \\
\hline
SLQMHead & \textbf{96.96} & \textbf{77.96} & \textbf{98.36} & \textbf{87.36} & \textbf{2.42} & \textbf{5.6} & \textbf{4.9} \\
\hline
\end{tabular}
\end{table}


\subsubsection{Ablation Study}

To investigate the synergistic gains among the innovative modules, we designed a comprehensive ablation study (see Table~\ref{tab:ablation}). The results clearly show a progressive improvement in performance. The baseline model's AP50-95 was 83.03\%, and introducing any single module brought performance gains. Among them, ABFPN's contribution was the most significant, once again highlighting the central role of feature alignment in the insulator detection task.

\begin{table}[!t]
\caption{Ablation Study Results\label{tab:ablation}}
\centering
\footnotesize
\begin{tabular}{|c|c|c|c|c|c|c|c|c|c|}
\hline
\textbf{C3k2\_HTC} & \textbf{ABFPN} & \textbf{SLQMH} & \textbf{GFLOPs} & \textbf{Model Size} & \textbf{P/\%} & \textbf{R/\%} & \textbf{F1/\%} & \textbf{AP50/\%} & \textbf{AP50-95/\%} \\
 &  &  &  & \textbf{(MB)} &  &  &  &  &  \\
\hline
× & × & × & 6.3 & 5.2 & 97.39 & 98.68 & 98.03 & 97.13 & 83.03 \\
\hline
√ & × & × & 6.2 & 5.0 & 95.92 & 94.44 & 95.15 & 98.10 & 84.61 \\
\hline
× & √ & × & 6.3 & 5.2 & 95.77 & 94.08 & 94.88 & 97.98 & 85.07 \\
\hline
× & × & √ & 5.6 & 4.9 & 93.71 & 95.58 & 94.59 & 97.97 & 84.03 \\
\hline
√ & √ & × & 6.1 & 4.9 & 95.72 & 92.78 & 94.16 & 97.45 & 84.17 \\
\hline
√ & × & √ & 5.5 & 4.7 & 95.50 & 93.69 & 94.50 & 97.77 & 83.53 \\
\hline
× & √ & √ & 5.6 & 4.8 & 95.54 & 93.06 & 94.21 & 97.35 & 83.58 \\
\hline
√ & √ & √ & \textbf{5.4} & \textbf{4.6} & \textbf{96.19} & \textbf{96.07} & \textbf{96.11} & \textbf{98.36} & \textbf{87.81} \\
\hline
\end{tabular}
\end{table}

Crucially, when all three modules were integrated, the model's performance showed a significant synergistic effect rather than a simple sum. The final YOLO-CEA model achieved an AP50-95 of \textbf{87.81\%}, a remarkable improvement of \textbf{4.78 percentage points} over the baseline model. At the same time, the model's GFLOPs and size were reduced to \textbf{5.4} and \textbf{4.6MB}, respectively, making it the most efficient configuration of all. This fully demonstrates that the context-enhanced features from C3k2-HTC, the aligned and balanced features from ABFPN, and the high-reliability predictions from SLQMHead form a highly complementary system that achieves an overall performance optimization of the baseline model.

\section{Conclusion}

This paper presents YOLO-CEA, a novel lightweight and high-performance detector specifically designed for insulator defect detection in power transmission lines. Through systematic innovations across the backbone, neck, and head components, YOLO-CEA successfully addresses the key challenges of complex backgrounds, significant scale variations, and computational constraints inherent in UAV-based inspection systems.

The proposed C3k2-HTC module effectively combines the global context modeling capabilities of Transformers with the local feature extraction strengths of CNNs, enabling superior feature representation in complex scenarios. The AB-FPN architecture introduces SNI and GSConvE modules to achieve precise multi-scale feature alignment while maintaining computational efficiency. The SharedLQMHead mechanism enhances both parameter efficiency and prediction reliability through cross-scale parameter sharing and location quality modulation.

Extensive experimental validation on the IDID and CPLID datasets demonstrates that YOLO-CEA achieves state-of-the-art performance with an AP50-95 of 87.81\%, representing a significant 4.78 percentage point improvement over the baseline while reducing computational complexity to 5.4 GFLOPs and model size to 4.6 MB. The ablation studies confirm the synergistic effects of the proposed modules, and comparative experiments validate the superior balance between accuracy and efficiency achieved by our approach.

The proposed YOLO-CEA model establishes a new benchmark for lightweight insulator defect detection, demonstrating significant potential for deployment in automated, resource-constrained aerial inspection systems. Future work will focus on extending the framework to other power equipment inspection tasks and exploring further optimizations for edge device deployment.

\section*{Acknowledgments}
[Acknowledgments section to be completed by the authors]

\begin{thebibliography}{50}
\bibliographystyle{IEEEtran}

\bibitem{ref1}
[Reference 1 - to be completed]

\bibitem{ref2}
[Reference 2 - to be completed]

\bibitem{ref3}
[Reference 3 - to be completed]

\bibitem{ref4}
[Reference 4 - to be completed]

\bibitem{ref5}
[Reference 5 - to be completed]

\bibitem{ref6}
[Reference 6 - to be completed]

\bibitem{ref7}
[Reference 7 - to be completed]

\bibitem{ref8}
[Reference 8 - to be completed]

\bibitem{ref9}
[Reference 9 - to be completed]

\bibitem{ref10}
[Reference 10 - to be completed]

\bibitem{ref11}
[Reference 11 - to be completed]

\bibitem{ref12}
[Reference 12 - to be completed]

\bibitem{ref13}
[Reference 13 - to be completed]

\bibitem{ref14}
[Reference 14 - to be completed]

\bibitem{ref15}
[Reference 15 - to be completed]

\bibitem{ref16}
[Reference 16 - to be completed]

\bibitem{ref17}
[Reference 17 - to be completed]

\bibitem{ref18}
[Reference 18 - to be completed]

\bibitem{ref19}
[Reference 19 - to be completed]

\bibitem{ref20}
[Reference 20 - to be completed]

\bibitem{ref21}
[Reference 21 - to be completed]

\bibitem{ref22}
[Reference 22 - to be completed]

\bibitem{ref23}
[Reference 23 - to be completed]

\bibitem{ref24}
[Reference 24 - to be completed]

\bibitem{ref25}
[Reference 25 - to be completed]

\bibitem{ref26}
[Reference 26 - to be completed]

\bibitem{ref27}
[Reference 27 - to be completed]

\bibitem{ref28}
[Reference 28 - to be completed]

\bibitem{ref36}
[Reference 36 - to be completed]

\bibitem{ref38}
[Reference 38 - to be completed]

\bibitem{ref39}
[Reference 39 - to be completed]
T. W. Chaundy, P. R. Barrett and C. Batey, {\it{The Printing of Mathematics}}. London, U.K., Oxford Univ. Press, 1954.

\bibitem{ref3}
F. Mittelbach and M. Goossens, {\it{The \LaTeX Companion}}, 2nd ed. Boston, MA, USA: Pearson, 2004.

\bibitem{ref4}
G. Gr\"atzer, {\it{More Math Into LaTeX}}, New York, NY, USA: Springer, 2007.

\bibitem{ref5}M. Letourneau and J. W. Sharp, {\it{AMS-StyleGuide-online.pdf,}} American Mathematical Society, Providence, RI, USA, [Online]. Available: http://www.ams.org/arc/styleguide/index.html

\bibitem{ref6}
H. Sira-Ramirez, ``On the sliding mode control of nonlinear systems,'' \textit{Syst. Control Lett.}, vol. 19, pp. 303--312, 1992.

\bibitem{ref7}
A. Levant, ``Exact differentiation of signals with unbounded higher derivatives,''  in \textit{Proc. 45th IEEE Conf. Decis.
Control}, San Diego, CA, USA, 2006, pp. 5585--5590. DOI: 10.1109/CDC.2006.377165.

\bibitem{ref8}
M. Fliess, C. Join, and H. Sira-Ramirez, ``Non-linear estimation is easy,'' \textit{Int. J. Model., Ident. Control}, vol. 4, no. 1, pp. 12--27, 2008.

\bibitem{ref9}
R. Ortega, A. Astolfi, G. Bastin, and H. Rodriguez, ``Stabilization of food-chain systems using a port-controlled Hamiltonian description,'' in \textit{Proc. Amer. Control Conf.}, Chicago, IL, USA,
2000, pp. 2245--2249.

\end{thebibliography}


\newpage

\section{Biography Section}
If you have an EPS/PDF photo (graphicx package needed), extra braces are
 needed around the contents of the optional argument to biography to prevent
 the LaTeX parser from getting confused when it sees the complicated
 $\backslash${\tt{includegraphics}} command within an optional argument. (You can create
 your own custom macro containing the $\backslash${\tt{includegraphics}} command to make things
 simpler here.)
 
\vspace{11pt}

\bf{If you include a photo:}\vspace{-33pt}
\begin{IEEEbiography}[{\includegraphics[width=1in,height=1.25in,clip,keepaspectratio]{fig1}}]{Michael Shell}
Use $\backslash${\tt{begin\{IEEEbiography\}}} and then for the 1st argument use $\backslash${\tt{includegraphics}} to declare and link the author photo.
Use the author name as the 3rd argument followed by the biography text.
\end{IEEEbiography}

\vspace{11pt}

\bf{If you will not include a photo:}\vspace{-33pt}
\begin{IEEEbiographynophoto}{John Doe}
Use $\backslash${\tt{begin\{IEEEbiographynophoto\}}} and the author name as the argument followed by the biography text.
\end{IEEEbiographynophoto}




\vfill

\end{document}


